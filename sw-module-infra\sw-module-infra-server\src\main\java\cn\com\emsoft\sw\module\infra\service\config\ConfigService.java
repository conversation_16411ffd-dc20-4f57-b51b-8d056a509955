package cn.com.emsoft.sw.module.infra.service.config;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.module.infra.controller.admin.config.vo.BizConfigCreateReqVO;
import cn.com.emsoft.sw.module.infra.controller.admin.config.vo.BizConfigUpdateReqVO;
import cn.com.emsoft.sw.module.infra.controller.admin.config.vo.ConfigPageReqVO;
import cn.com.emsoft.sw.module.infra.controller.admin.config.vo.ConfigSaveReqVO;
import cn.com.emsoft.sw.module.infra.dal.dataobject.config.ConfigDO;

import jakarta.validation.Valid;

import java.util.List;

/**
 * 参数配置 Service 接口
 *
 * <AUTHOR>
 */
public interface ConfigService {

    /**
     * 创建参数配置
     *
     * @param createReqVO 创建信息
     * @return 配置编号
     */
    Long createConfig(@Valid ConfigSaveReqVO createReqVO);

    /**
     * 抄表参数&参数配置创建
     * @param createReqVO 抄表参数&参数配置
     * @return id
     */
    Long createMeterOrParameterConfig(@Valid BizConfigCreateReqVO createReqVO);

    /**
     * 更新参数配置
     *
     * @param updateReqVO 更新信息
     */
    void updateConfig(@Valid ConfigSaveReqVO updateReqVO);
    /**
     * 更新参数配置
     *
     * @param updateReqVO 更新信息
     */
    void updateMeterOrParameterConfig(@Valid BizConfigUpdateReqVO updateReqVO);

    /**
     * 删除参数配置
     *
     * @param id 配置编号
     */
    void deleteConfig(Long id);

    /**
     * 获得参数配置
     *
     * @param id 配置编号
     * @return 参数配置
     */
    ConfigDO getConfig(Long id);

    /**
     * 根据参数键，获得参数配置
     *
     * @param key 配置键
     * @return 参数配置
     */
    ConfigDO getConfigByKey(String key);

    /**
     * 获得参数配置分页列表
     *
     * @param reqVO 分页条件
     * @return 分页列表
     */
    PageResult<ConfigDO> getConfigPage(ConfigPageReqVO reqVO);

    /**
     * 根据参数类型和分组获取配置列表
     *
     * @param type 参数类型
     * @param category 参数分组
     * @return 配置列表
     */
    List<ConfigDO> getConfigListByTypeAndCategory(Integer type, String category);

    /**
     * 根据参数类型获取不重复的参数分组列表
     *
     * @param type 参数类型
     * @return 参数分组列表，按升序排序
     */
    List<String> getCategoriesByType(Integer type);

}
