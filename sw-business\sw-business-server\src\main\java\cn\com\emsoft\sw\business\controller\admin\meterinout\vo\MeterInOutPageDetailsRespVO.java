package cn.com.emsoft.sw.business.controller.admin.meterinout.vo;

import cn.com.emsoft.sw.business.enums.DictTypeConstants;
import cn.com.emsoft.sw.framework.excel.core.annotations.DictFormat;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;


@Schema(description = "管理后台 - 入/出库明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MeterInOutPageDetailsRespVO {
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2807")
    private Long id;

    @Schema(description = "所属仓库主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "14736")
    private Long deptId;

    @Schema(description = "所属仓库", requiredMode = Schema.RequiredMode.REQUIRED, example = "测试仓库")
    @ExcelProperty({"出入库明细清单","所属仓库"})
    private String deptName;

    @Schema(description = "库存单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty({"出入库明细清单","入/出库单编号"})
    private String stockCode;

    @Schema(description = "钢印号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty({"出入库明细清单","钢印号"})
    private String steelMark;

    @Schema(description = "水表编号")
    @ExcelProperty({"出入库明细清单","水表编号"})
    private String sealNumber;

    @Schema(description = "条形码")
    @ExcelProperty({"出入库明细清单","条形码"})
    private String barCode;

    @Schema(description = "强检编号")
    @ExcelProperty({"出入库明细清单","强检编号"})
    private String checkCode;

    @Schema(description = "强检日期")
    @ExcelProperty({"出入库明细清单","强检日期"})
    private LocalDateTime checkDate;

    @Schema(description = "生产日期")
    @ExcelProperty({"出入库明细清单","生产日期"})
    private LocalDateTime makeDate;

    @ExcelProperty({"出入库明细清单", "水表厂家"})
    private String makerName;

    @ExcelProperty({"出入库明细清单", "水表型号"})
    private String modelName;

    @ExcelProperty({"出入库明细清单", "水表口径"})
    private String caliberName;

    @ExcelProperty({"出入库明细清单", "水表量程"})
    private String rangeName;

    @Schema(description = "水表分类/类型", example = "1")
    private Short type;

    @Schema(description = "水表分类/类型名称", example = "1")
    @DictFormat(DictTypeConstants.METER_OTH_TYPE)
    @ExcelProperty({"出入库明细清单","水表类型"})
    private String typeName;

    @Schema(description = "出/入库人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty({"出入库明细清单","出/入库人"})
    private String operationUser;

    @Schema(description = "出/入库时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty({"出入库明细清单","出/入库时间"})
    private LocalDateTime operationTime;

    @Schema(description = "备注", example = "水表信息表-biz_meter中的备注")
    @ExcelProperty({"出入库明细清单","备注"})
    private String remark;

    @Schema(description = "厂家信息")
    private MakerInfo maker;

    @Schema(description = "型号信息")
    private ModelInfo model;

    @Schema(description = "口径信息")
    private CaliberInfo caliber;

    @Schema(description = "量程信息")
    private RangeInfo range;

    @Data
    @Schema(description = "厂家信息")
    public static class MakerInfo {

        @Schema(description = "厂家主键", example = "1001")
        private Long id;

        @Schema(description = "厂家代码", example = "MAKER-001")
        private String code;

        @Schema(description = "厂家名称", example = "测试厂家")
        @ExcelProperty({"出入库明细清单","水表厂家"})
        private String name;
    }

    @Data
    @Schema(description = "型号信息")
    public static class ModelInfo {

        @Schema(description = "型号主键", example = "2001")
        private Long id;

        @Schema(description = "型号代码", example = "WISCOM-LXSY-15E")
        private String code;

        @Schema(description = "型号名称", example = "LXSY-15E")
        @ExcelProperty({"出入库明细清单","水表型号"})
        private String name;
    }

    @Data
    @Schema(description = "口径信息")
    public static class CaliberInfo {

        @Schema(description = "口径主键", example = "3001")
        private Long id;

        @Schema(description = "口径代码", example = "DN20")
        private String code;

        @Schema(description = "口径名称", example = "20毫米")
        @ExcelProperty({"出入库明细清单","水表口径"})
        private String name;
    }

    @Data
    @Schema(description = "量程信息")
    public static class RangeInfo {

        @Schema(description = "量程主键", example = "4001")
        private Long id;

        @Schema(description = "量程代码", example = "RANGE_9999")
        private String code;

        @Schema(description = "量程名称", example = "9999")
        @ExcelProperty({"出入库明细清单","水表量程"})
        private String name;
    }
}
