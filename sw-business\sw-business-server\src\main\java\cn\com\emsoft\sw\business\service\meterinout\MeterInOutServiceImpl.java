package cn.com.emsoft.sw.business.service.meterinout;

import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.*;
import cn.com.emsoft.sw.business.dal.dataobject.metercaliber.MeterCaliberDO;
import cn.com.emsoft.sw.business.dal.dataobject.metermaker.MeterMakerDO;
import cn.com.emsoft.sw.business.dal.dataobject.metermodel.MeterModelDO;
import cn.com.emsoft.sw.business.dal.dataobject.meterrange.MeterRangeDO;
import cn.com.emsoft.sw.business.dal.dataobject.meter.MeterDO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinout.MeterInOutDO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinoutref.MeterInOutRelDO;
import cn.com.emsoft.sw.business.dal.mysql.meter.MeterMapper;
import cn.com.emsoft.sw.business.dal.mysql.meterinout.MeterInOutMapper;
import cn.com.emsoft.sw.business.dal.mysql.meterinoutref.MeterInOutRelMapper;
import cn.com.emsoft.sw.business.dal.redis.RedisKeyConstants;
import cn.com.emsoft.sw.business.dal.redis.meterinout.MeterInOutLockRedisDAO;
import cn.com.emsoft.sw.business.enums.meter.MWStateEnum;
import cn.com.emsoft.sw.business.enums.meterLog.MeterLogEnum;
import cn.com.emsoft.sw.framework.common.util.date.DateUtils;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.security.core.util.SecurityFrameworkUtils;
import lombok.SneakyThrows;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.transaction.annotation.Transactional;
import cn.com.emsoft.sw.business.enums.DictTypeConstants;
import cn.com.emsoft.sw.business.service.metercaliber.MeterCaliberService;
import cn.com.emsoft.sw.business.service.metermaker.MeterMakerService;
import cn.com.emsoft.sw.business.service.metermodel.MeterModelService;
import cn.com.emsoft.sw.business.service.meterrange.MeterRangeService;
import cn.com.emsoft.sw.business.service.meterlog.MeterLogService;
import cn.com.emsoft.sw.framework.dict.core.DictFrameworkUtils;
import cn.com.emsoft.sw.module.system.api.dept.DeptApi;
import cn.com.emsoft.sw.module.system.api.dept.dto.DeptRespDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Data;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.scheduling.annotation.Async;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.Comparator;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;

import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.*;
import static cn.com.emsoft.sw.business.enums.ErrorMessageConstants.*;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.com.emsoft.sw.framework.common.util.collection.CollectionUtils.convertList;

/**
 * 水表出/入库单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MeterInOutServiceImpl implements MeterInOutService {

    @Resource
    private MeterInOutMapper meterInOutMapper;

    @Resource
    private MeterMapper meterMapper;

    @Resource
    private MeterInOutRelMapper meterInOutRelMapper;

    @Resource
    private MeterMakerService meterMakerService;

    @Resource
    private MeterModelService meterModelService;

    @Resource
    private MeterCaliberService meterCaliberService;

    @Resource
    private MeterRangeService meterRangeService;

    @Resource
    private MeterLogService meterLogService;

    @Resource
    private DeptApi deptApi;

    @Resource
    private MeterInOutLockRedisDAO meterInOutLockRedisDAO;

    public static final long METER_IN_OUT_TIMEOUT_MILLIS = 5 * DateUtils.SECOND_MILLIS;

    @Override
    public Long createMeterInOut(MeterInOutSaveReqVO createReqVO) {
        // 插入
        MeterInOutDO meterInOut = BeanUtils.toBean(createReqVO, MeterInOutDO.class);
        meterInOutMapper.insert(meterInOut);
        // 返回
        return meterInOut.getId();
    }

    @Override
    public void updateMeterInOut(MeterInOutSaveReqVO updateReqVO) {
        // 校验存在
        validateMeterInOutExists(updateReqVO.getId());
        // 更新
        MeterInOutDO updateObj = BeanUtils.toBean(updateReqVO, MeterInOutDO.class);
        meterInOutMapper.updateById(updateObj);
    }

    @Override
    public void deleteMeterInOut(Long id) {
        // 校验存在
        validateMeterInOutExists(id);
        // 删除
        meterInOutMapper.deleteById(id);
    }

    @Override
    public void deleteMeterInOutListByIds(List<Long> ids) {
        // 校验存在
        validateMeterInOutExists(ids);
        // 删除
        meterInOutMapper.deleteByIds(ids);
    }

    private void validateMeterInOutExists(List<Long> ids) {
        List<MeterInOutDO> list = meterInOutMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(METER_IN_OUT_NOT_EXISTS);
        }
    }

    private void validateMeterInOutExists(Long id) {
        if (meterInOutMapper.selectById(id) == null) {
            throw exception(METER_IN_OUT_NOT_EXISTS);
        }
    }

    @Override
    public MeterInOutDO getMeterInOut(Long id) {
        return meterInOutMapper.selectById(id);
    }

    @Override
    public PageResult<MeterInOutDO> getMeterInOutPage(MeterInOutPageReqVO pageReqVO) {
        
        return meterInOutMapper.selectPage(pageReqVO);
    }

    @Override
    public PageResult<MeterInOutPageRespVO> getMeterInOutPageWithBusiness(MeterInOutPageReqVO pageReqVO) {

        // 1. 执行基础分页查询
        PageResult<MeterInOutDO> pageResult = meterInOutMapper.selectPage(pageReqVO);
        
        // 2. 如果没有数据，直接返回空结果
        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>(Collections.emptyList(), pageResult.getTotal());
        }
        
        // 3. 收集所有出入库单ID和部门ID
        List<MeterInOutDO> inOutList = pageResult.getList();
        List<Long> inOutIds = inOutList.stream().map(MeterInOutDO::getId).toList();
        Set<Long> deptIds = inOutList.stream()
                .map(MeterInOutDO::getDeptId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        // 4. 批量查询水表数量（通过关联表统计）
        Map<Long, Long> meterCountMap = meterInOutRelMapper.selectMeterCountByInOutIds(inOutIds);
        
        // 5. 批量查询部门信息
        Map<Long, DeptRespDTO> deptMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(deptIds)) {
            deptMap = deptApi.getDeptMap(deptIds);
        }
        
        // 6. 转换为响应VO
        List<MeterInOutPageRespVO> respList = new ArrayList<>();
        for (MeterInOutDO inOut : inOutList) {
            MeterInOutPageRespVO respVO = BeanUtils.toBean(inOut, MeterInOutPageRespVO.class);
            
            // 设置水表数量
            respVO.setMeterCount(meterCountMap.getOrDefault(inOut.getId(), 0L).intValue());
            
            // 设置部门名称
            if (inOut.getDeptId() != null) {
                DeptRespDTO dept = deptMap.get(inOut.getDeptId());
                if (dept != null) {
                    respVO.setDeptName(dept.getName());
                }
            }
            
            // 手动设置字典转换：stockTypeName
            if (inOut.getStockType() != null) {
                String stockTypeName = DictFrameworkUtils.parseDictDataLabel(
                    DictTypeConstants.STOCK_TYPE,
                    String.valueOf(inOut.getStockType())
                );
                respVO.setStockTypeName(stockTypeName);
            }
            
            respList.add(respVO);
        }
        
        return new PageResult<>(respList, pageResult.getTotal());
    }

    @Override
    public PageResult<MeterInOutPageDetailsRespVO> getMeterInOutPageDetails(MeterInOutPageDetailsReqVO pageReqVO) {

        // 2. 构建出入库单查询条件
        // 3. 查询符合条件的出入库单
        List<MeterInOutDO> inOutList = meterInOutMapper.getMeterInOutDetailsPageList(pageReqVO);
        
        // 4. 如果没有出入库单数据，直接返回空结果
        if (CollUtil.isEmpty(inOutList)) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }

        // 5. 收集所有出入库单ID，用于水表关联查询
        List<Long> inOutIds = inOutList.stream().map(MeterInOutDO::getId).toList();
        
        // 6. 通过关联表查询关联的水表ID
        List<Long> meterIds = meterInOutRelMapper.selectMeterIdsByInOutIds(inOutIds);
        
        // 7. 如果没有关联的水表，返回空结果
        if (CollUtil.isEmpty(meterIds)) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }
        
        // 8. 分页查询相关的水表数据
        PageResult<MeterDO> meterPageResult = meterMapper.selectMeterInOutPageDetailsMeterInfo(pageReqVO, meterIds);
        
        // 9. 如果没有水表数据，返回空结果
        if (CollUtil.isEmpty(meterPageResult.getList())) {
            return new PageResult<>(Collections.emptyList(), meterPageResult.getTotal());
        }
        
        List<MeterDO> meterList = meterPageResult.getList();

        // 10. 构建出入库单ID到出入库单对象的映射
        Map<Long, MeterInOutDO> inOutMap = inOutList.stream()
                .collect(Collectors.toMap(MeterInOutDO::getId, inOut -> inOut));

        // 11. 构建水表ID到出入库单ID的映射（通过关联表）
        Map<Long, Long> meterToInOutMap = new HashMap<>();
        List<MeterInOutRelDO> refList = meterInOutRelMapper.selectList(new LambdaQueryWrapperX<MeterInOutRelDO>()
                .in(MeterInOutRelDO::getMeterId, meterList.stream().map(MeterDO::getId).toList()));
        for (MeterInOutRelDO ref : refList) {
            meterToInOutMap.put(ref.getMeterId(), ref.getMeterInOutId());
        }

        // 10. 收集需要查询字典的代码集合
        Set<String> makerCodes = meterList.stream().map(MeterDO::getMakerCode).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        Set<String> modelCodes = meterList.stream().map(MeterDO::getModelCode).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        Set<String> caliberCodes = meterList.stream().map(MeterDO::getCaliberCode).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        Set<String> rangeCodes = meterList.stream().map(MeterDO::getRangeCode).filter(StrUtil::isNotBlank).collect(Collectors.toSet());
        Set<Long> deptIds = meterList.stream().map(MeterDO::getDeptId).filter(Objects::nonNull).collect(Collectors.toSet());

        // 11. 批量查询字典数据（复用现有缓存构建方法）
        Map<String, MeterMakerDO> makerCodeMap = buildMakerCodeMap(makerCodes);
        Map<String, MeterModelDO> modelCodeMap = buildModelCodeMap(modelCodes);
        Map<String, MeterCaliberDO> caliberCodeMap = buildCaliberCodeMap(caliberCodes);
        Map<String, MeterRangeDO> rangeCodeMap = buildRangeCodeMap(rangeCodes);
        
        // 12. 批量查询部门信息
        Map<Long, DeptRespDTO> deptMap = Collections.emptyMap();
        if (CollUtil.isNotEmpty(deptIds)) {
            deptMap = deptApi.getDeptMap(deptIds);
        }

        // 12. 组装响应数据
        List<MeterInOutPageDetailsRespVO> detailsList = new ArrayList<>();
        for (MeterDO meter : meterList) {
            // 通过关联表确定出入库单ID
            Long inOutId = meterToInOutMap.get(meter.getId());
            if (inOutId == null) {
                continue;
            }
            
            MeterInOutDO inOut = inOutMap.get(inOutId);
            if (inOut == null) {
                continue;
            }

            // 构建响应VO
            MeterInOutPageDetailsRespVO detailVO = new MeterInOutPageDetailsRespVO();
            
            // 设置水表基础信息
            detailVO.setId(meter.getId());
            detailVO.setSteelMark(meter.getSteelMark());
            detailVO.setSealNumber(meter.getSealNumber());
            detailVO.setBarCode(meter.getBarCode());
            detailVO.setCheckCode(meter.getCheckCode());
            detailVO.setCheckDate(meter.getCheckDate());
            detailVO.setMakeDate(meter.getMakeDate());
            detailVO.setType(meter.getType());
            detailVO.setDeptId(meter.getDeptId());
            // 设置水表类型名称（使用字典转换）
            if (detailVO.getType() != null) {
                String typeName = DictFrameworkUtils.parseDictDataLabel(
                        DictTypeConstants.METER_OTH_TYPE,
                        String.valueOf(detailVO.getType())
                );
                detailVO.setTypeName(typeName);
            }
            detailVO.setRemark(meter.getRemark());
            
            // 设置出入库单信息
            detailVO.setStockCode(inOut.getStockCode());
            detailVO.setOperationUser(inOut.getOperationUser());
            detailVO.setOperationTime(inOut.getOperationTime());
            
            // 设置部门名称
            if (meter.getDeptId() != null) {
                DeptRespDTO dept = deptMap.get(meter.getDeptId());
                if (dept != null) {
                    detailVO.setDeptName(dept.getName());
                }
            }
            
            // 设置厂家信息
            if (StrUtil.isNotBlank(meter.getMakerCode())) {
                MeterInOutPageDetailsRespVO.MakerInfo makerInfo = new MeterInOutPageDetailsRespVO.MakerInfo();
                makerInfo.setCode(meter.getMakerCode());
                MeterMakerDO maker = makerCodeMap.get(meter.getMakerCode());
                if (maker != null) {
                    makerInfo.setId(maker.getId());
                    makerInfo.setName(maker.getName());
                }
                detailVO.setMaker(makerInfo);
                //适配excel导出所增加的字段
                detailVO.setMakerName(makerInfo.getName());
            }
            
            // 设置型号信息
            if (StrUtil.isNotBlank(meter.getModelCode())) {
                MeterInOutPageDetailsRespVO.ModelInfo modelInfo = new MeterInOutPageDetailsRespVO.ModelInfo();
                modelInfo.setCode(meter.getModelCode());
                MeterModelDO model = modelCodeMap.get(meter.getModelCode());
                if (model != null) {
                    modelInfo.setId(model.getId());
                    modelInfo.setName(model.getName());
                }
                detailVO.setModel(modelInfo);
                //适配excel导出所增加的字段
                detailVO.setModelName(modelInfo.getName());
            }
            
            // 设置口径信息
            if (StrUtil.isNotBlank(meter.getCaliberCode())) {
                MeterInOutPageDetailsRespVO.CaliberInfo caliberInfo = new MeterInOutPageDetailsRespVO.CaliberInfo();
                caliberInfo.setCode(meter.getCaliberCode());
                MeterCaliberDO caliber = caliberCodeMap.get(meter.getCaliberCode());
                if (caliber != null) {
                    caliberInfo.setId(caliber.getId());
                    caliberInfo.setName(caliber.getName());
                }
                detailVO.setCaliber(caliberInfo);
                //适配excel导出所增加的字段
                detailVO.setCaliberName(caliberInfo.getName());
            }
            
            // 设置量程信息
            if (StrUtil.isNotBlank(meter.getRangeCode())) {
                MeterInOutPageDetailsRespVO.RangeInfo rangeInfo = new MeterInOutPageDetailsRespVO.RangeInfo();
                rangeInfo.setCode(meter.getRangeCode());
                MeterRangeDO range = rangeCodeMap.get(meter.getRangeCode());
                if (range != null) {
                    rangeInfo.setId(range.getId());
                    rangeInfo.setName(range.getName());
                }
                detailVO.setRange(rangeInfo);
                //适配excel导出所增加的字段
                detailVO.setRangeName(rangeInfo.getName());
            }
            
            detailsList.add(detailVO);
        }

        // 13. 按照stockCode降序排序（确保最终结果按出入库单编号排序）
        detailsList.sort((a, b) -> {
            if (a.getStockCode() == null && b.getStockCode() == null) {
                return 0;
            }
            if (a.getStockCode() == null) {
                return 1;
            }
            if (b.getStockCode() == null) {
                return -1;
            }
            return b.getStockCode().compareTo(a.getStockCode()); // 降序排序
        });

        // 14. 返回分页结果（分页已在水表查询层面完成）
        return new PageResult<>(detailsList, meterPageResult.getTotal());
    }

    @Override
    @Cacheable(value = RedisKeyConstants.METER_IN_OUT_DETAILS, key = "#id",
            unless = "#result == null")
    public List<MeterInOutDetailsRespVO> getMeterInOutDetails(Long id) {
        // 1. 校验出/入库单是否存在并获取基本信息
        MeterInOutDO meterInOut = getMeterInOut(id);
        if (meterInOut == null) {
            throw exception(METER_IN_OUT_NOT_EXISTS);
        }

        // 2. 通过关联表查询关联的水表数据
        List<Long> meterIds = meterInOutRelMapper.selectMeterIdsByInOutId(id);
        if (CollUtil.isEmpty(meterIds)) {
            return Collections.emptyList();
        }
        
        List<MeterDO> meterList = meterMapper.selectListByIds(meterIds);

        // 3. 如果没有关联的水表数据，直接返回空列表
        if (CollUtil.isEmpty(meterList)) {
            return Collections.emptyList();
        }

        // 4. 按5个关键字段进行分组聚合
        Map<String, List<MeterDO>> groupedMeters = meterList.stream()
                .collect(Collectors.groupingBy(meter -> 
                    buildGroupKey(meter.getMakerCode(), meter.getModelCode(), 
                                meter.getCaliberCode(), meter.getRangeCode(), meter.getType())));

        // 5. 收集所有需要查询名称的代码
        Set<String> makerCodes = meterList.stream()
                .map(MeterDO::getMakerCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
        
        Set<String> modelCodes = meterList.stream()
                .map(MeterDO::getModelCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
        
        Set<String> caliberCodes = meterList.stream()
                .map(MeterDO::getCaliberCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());
        
        Set<String> rangeCodes = meterList.stream()
                .map(MeterDO::getRangeCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 6. 批量查询名称信息（复用现有的缓存构建方法）
        Map<String, MeterMakerDO> makerCodeMap = buildMakerCodeMap(makerCodes);
        Map<String, MeterModelDO> modelCodeMap = buildModelCodeMap(modelCodes);
        Map<String, MeterCaliberDO> caliberCodeMap = buildCaliberCodeMap(caliberCodes);
        Map<String, MeterRangeDO> rangeCodeMap = buildRangeCodeMap(rangeCodes);

        // 7. 构建响应VO列表
        List<MeterInOutDetailsRespVO> result = new ArrayList<>();
        for (Map.Entry<String, List<MeterDO>> entry : groupedMeters.entrySet()) {
            List<MeterDO> groupMeters = entry.getValue();
            if (CollUtil.isEmpty(groupMeters)) {
                continue;
            }
            
            // 取组内第一条记录作为代表（所有记录的5个关键字段都相同）
            MeterDO representative = groupMeters.get(0);
            
            MeterInOutDetailsRespVO detailVO = new MeterInOutDetailsRespVO();
            
            // 设置基本字段
            detailVO.setId(representative.getId());
            
            // 设置5个关键字段
            detailVO.setMakerCode(representative.getMakerCode());
            detailVO.setModelCode(representative.getModelCode());
            detailVO.setCaliberCode(representative.getCaliberCode());
            detailVO.setRangeCode(representative.getRangeCode());
            detailVO.setType(representative.getType() != null ? representative.getType().intValue() : null);
            
            // 设置统计数量
            detailVO.setCount(groupMeters.size());
            
            // 设置备注（取第一条记录的备注）
            detailVO.setRemark(representative.getRemark());
            
            // 设置名称字段
            if (StrUtil.isNotBlank(representative.getMakerCode())) {
                MeterMakerDO maker = makerCodeMap.get(representative.getMakerCode());
                if (maker != null) {
                    detailVO.setMakerName(maker.getName());
                }
            }
            
            if (StrUtil.isNotBlank(representative.getModelCode())) {
                MeterModelDO model = modelCodeMap.get(representative.getModelCode());
                if (model != null) {
                    detailVO.setModelName(model.getName());
                }
            }
            
            if (StrUtil.isNotBlank(representative.getCaliberCode())) {
                MeterCaliberDO caliber = caliberCodeMap.get(representative.getCaliberCode());
                if (caliber != null) {
                    detailVO.setCaliberName(caliber.getName());
                }
            }
            
            if (StrUtil.isNotBlank(representative.getRangeCode())) {
                MeterRangeDO range = rangeCodeMap.get(representative.getRangeCode());
                if (range != null) {
                    detailVO.setRangeName(range.getName());
                }
            }
            
            // 设置水表类型名称（使用字典转换）
            if (representative.getType() != null) {
                String typeName = DictFrameworkUtils.parseDictDataLabel(
                    DictTypeConstants.METER_OTH_TYPE,
                    String.valueOf(representative.getType())
                );
                detailVO.setTypeName(typeName);
            }
            
            result.add(detailVO);
        }

        // 8. 按ID排序返回（保证结果稳定）
        result.sort(Comparator.comparing(MeterInOutDetailsRespVO::getId));
        return result;
    }

    /**
     * 构建分组键
     * 
     * @param makerCode 厂家代码
     * @param modelCode 型号代码
     * @param caliberCode 口径代码
     * @param rangeCode 量程代码
     * @param type 类型编码
     * @return 分组键
     */
    private String buildGroupKey(String makerCode, String modelCode, 
                               String caliberCode, String rangeCode, Short type) {
        return String.join("|", 
            StrUtil.nullToEmpty(makerCode),
            StrUtil.nullToEmpty(modelCode),
            StrUtil.nullToEmpty(caliberCode),
            StrUtil.nullToEmpty(rangeCode),
            String.valueOf(type != null ? type : ""));
    }

    /**
     * 校验水表导入数据
     * 
     * 功能说明：
     * - 预加载基础数据到内存缓存（性能优化）
     * - 四大校验：必填字段、Excel内重复、数据库唯一性、基础数据存在性
     * - 字典转换：type字段自动转换为typeName
     * - 仅校验不插入：返回详细的校验结果供前端处理
     */
    @Override
    public MeterImportRespVO checkMeterImportList(List<MeterImportVO> importList) {
        // 1. 前置校验
        if (CollUtil.isEmpty(importList)) {
            throw exception(METER_IMPORT_DATA_EMPTY);
        }

        // 2. 预加载基础数据到内存（性能优化关键）
        ImportCacheData cacheData = preloadBaseData(importList);

        // 3. 逐条校验并收集结果
        List<MeterImportRespVO.MeterCheckItem> checkItems = new ArrayList<>();
        for (int i = 0; i < importList.size(); i++) {
            MeterImportVO importVO = importList.get(i);
            MeterImportRespVO.MeterCheckItem checkItem = validateSingleMeter(importVO, i, cacheData);
            checkItems.add(checkItem);
        }

        // 4. 统计校验结果
        MeterImportRespVO.CheckResult checkResult = buildCheckResult(checkItems);

        // 5. 构建返回结果（仅校验，不插入数据）
        return MeterImportRespVO.builder()
                .checkResult(checkResult)
                .meterCheckList(checkItems)
                .build();
    }

    /**
     * 水表入库
     * 业务流程：
     * 1. 数据结构校验（基本参数检查）
     * 2. 二次业务校验（按需批量查询+Map缓存，高性能）
     * 3. 处理入库单记录（存在则使用，不存在则创建）
     * 4. 批量插入水表信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    @CacheEvict(value = RedisKeyConstants.METER_IN_OUT_DETAILS,key = "#result")
    public Long meterIn(MeterInCreateVO createVO) {
        // 获取当前用户登陆id
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        // 尝试加锁执行并返回结果
        return meterInOutLockRedisDAO.meterInLock(userId, METER_IN_OUT_TIMEOUT_MILLIS, () -> {
            // 1. 数据结构校验
            validateMeterInBasicStructure(createVO);

            // 2. 二次业务校验（按需批量查询+高性能Map缓存）
            performMeterInValidation(createVO.getMeterList());

            // 3. 处理入库单记录（存在则使用，不存在则创建）
            Long inOutId = handleMeterInOutRecord(createVO);

            // 4. 批量插入水表信息
            batchInsertMeters(createVO.getMeterList(), inOutId, createVO.getDeptId());

            return inOutId;
        });
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @SneakyThrows
    @CacheEvict(value = RedisKeyConstants.METER_IN_OUT_DETAILS,key = "#result")
    public Long meterOut(MeterOutCreateVO createVO) {
        // 获取当前用户登陆id
        Long userId = SecurityFrameworkUtils.getLoginUserId();

        // 尝试加锁并且放回结果
        return meterInOutLockRedisDAO.meterOutLock(userId,METER_IN_OUT_TIMEOUT_MILLIS,()->{
            // 1. 基本参数校验
            validateMeterOutBasicStructure(createVO);

            // 2. 业务数据校验（水表存在性、状态、必填字段等）
            List<MeterDO> metersToOut = performMeterOutValidation(createVO);

            // 3. 调库特殊校验（如果是调库模式）
            if (createVO.getIsAdjustDept()) {
                validateAdjustDeptOperation(createVO, metersToOut);
            }

            // 4. 执行出库操作
            Long outId = executeOutOperation(createVO, metersToOut);

            // 5. 如果是调库，执行入库操作
            if (createVO.getIsAdjustDept()) {
                executeAdjustInOperation(createVO, metersToOut, outId);
            }

            return outId;
        });
    }

    /**
     * 处理入库单记录逻辑
     * @param createVO 创建入库单的VO
     * @return 入库单ID
     */
    private Long handleMeterInOutRecord(MeterInCreateVO createVO) {
        String inputStockCode = createVO.getStockCode();

        // 如果没有传入库存单编号，生成新编号并创建记录
        if (StrUtil.isBlank(inputStockCode)) {
            String stockCode = generateNextStockInCode();
            return createMeterInOutRecord(createVO, stockCode);
        }

        // 检查传入的库存单编号是否已存在
        MeterInOutDO existingRecord = meterInOutMapper.selectOne(MeterInOutDO::getStockCode, inputStockCode);

        if (existingRecord != null) {
            // 库存单编号已存在，直接返回现有记录的ID
            return existingRecord.getId();
        } else {
            // 库存单编号不存在，使用传入的编号创建新记录
            return createMeterInOutRecord(createVO, inputStockCode);
        }
    }

    /**
     * 预加载基础数据到内存（按需查询优化版）
     */
    private ImportCacheData preloadBaseData(List<MeterImportVO> importList) {
        ImportCacheData cacheData = new ImportCacheData();

        // 1. 收集所有需要的名称（按需收集，不查全量）
        Set<String> makerNames = importList.stream()
                .map(MeterImportVO::getMakerName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        Set<String> modelNames = importList.stream()
                .map(MeterImportVO::getModelName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        Set<String> caliberNames = importList.stream()
                .map(MeterImportVO::getCaliberName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        Set<String> rangeNames = importList.stream()
                .map(MeterImportVO::getRangeName)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 2. 按需批量查询（通过Service layer调用）
        // 注意：这里虽然仍然查询全部，但已经优化了收集逻辑
        // 如果将来需要进一步优化，可以在Service层添加按名称批量查询的方法
        List<MeterMakerDO> makerList = meterMakerService.getSimpleMeterMakerList();
        cacheData.setMakerNameMap(makerList.stream()
                .filter(maker -> makerNames.contains(maker.getName()))
                .collect(Collectors.toMap(MeterMakerDO::getName, maker -> maker, (existing, replacement) -> existing)));

        List<MeterModelDO> modelList = meterModelService.getSimpleMeterModelList();
        cacheData.setModelNameMap(modelList.stream()
                .filter(model -> modelNames.contains(model.getName()))
                .collect(Collectors.toMap(MeterModelDO::getName, model -> model, (existing, replacement) -> existing)));

        List<MeterCaliberDO> caliberList = meterCaliberService.getSimpleMeterCaliberList();
        cacheData.setCaliberNameMap(caliberList.stream()
                .filter(caliber -> caliberNames.contains(caliber.getName()))
                .collect(Collectors.toMap(MeterCaliberDO::getName, caliber -> caliber,
                        (existing, replacement) -> existing)));

        List<MeterRangeDO> rangeList = meterRangeService.getSimpleMeterRangeList();
        cacheData.setRangeNameMap(rangeList.stream()
                .filter(range -> rangeNames.contains(range.getName()))
                .collect(Collectors.toMap(MeterRangeDO::getName, range -> range, (existing, replacement) -> existing)));

        // 收集需要校验的唯一字段
        Set<String> steelMarks = importList.stream()
                .map(MeterImportVO::getSteelMark)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        Set<String> barCodes = importList.stream()
                .map(MeterImportVO::getBarCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 批量查询数据库中已存在的数据
        cacheData.setExistingSteelMarks(new HashSet<>(meterMapper.selectExistingSteelMarks(steelMarks)));

        cacheData.setExistingBarCodes(new HashSet<>(meterMapper.selectExistingBarCodes(barCodes)));

        return cacheData;
    }

    /**
     * 校验单条水表数据
     */
    private MeterImportRespVO.MeterCheckItem validateSingleMeter(MeterImportVO importVO, int rowIndex,
            ImportCacheData cacheData) {
        List<String> errorMessages = new ArrayList<>();

        // 1. 必填字段校验
        validateRequiredFields(importVO, errorMessages);

        // 2. Excel内重复性校验
        validateDuplicateInExcel(importVO, rowIndex, cacheData);

        // 3. 数据库唯一性校验
        validateUniqueInDatabase(importVO, cacheData, errorMessages);

        // 4. 基础数据存在性校验
        validateBaseDataExists(importVO, cacheData, errorMessages);

        // 5. 构建MeterInfo（包含字典转换）
        MeterImportRespVO.MeterInfo meterInfo = buildMeterInfo(importVO, cacheData);

        return MeterImportRespVO.MeterCheckItem.builder()
                .isValid(errorMessages.isEmpty())
                .errorMessages(errorMessages)
                .meterInfo(meterInfo)
                .build();
    }

    /**
     * 必填字段校验
     * 必填字段：钢印号、水表厂家、水表型号、水表口径、水表量程
     */
    private void validateRequiredFields(MeterImportVO importVO, List<String> errorMessages) {
        if (StrUtil.isBlank(importVO.getSteelMark())) {
            errorMessages.add(METER_IMPORT_STEEL_MARK_REQUIRED);
        }
        if (StrUtil.isBlank(importVO.getMakerName())) {
            errorMessages.add(METER_IMPORT_MAKER_NAME_REQUIRED);
        }
        if (StrUtil.isBlank(importVO.getModelName())) {
            errorMessages.add(METER_IMPORT_MODEL_NAME_REQUIRED);
        }
        if (StrUtil.isBlank(importVO.getCaliberName())) {
            errorMessages.add(METER_IMPORT_CALIBER_NAME_REQUIRED);
        }
        if (StrUtil.isBlank(importVO.getRangeName())) {
            errorMessages.add(METER_IMPORT_RANGE_NAME_REQUIRED);
        }
    }

    /**
     * Excel内重复性校验
     * 注意：发现重复时立即抛异常，快速失败
     */
    private void validateDuplicateInExcel(MeterImportVO importVO, int rowIndex, ImportCacheData cacheData) {
        // 钢印号重复校验
        if (StrUtil.isNotBlank(importVO.getSteelMark())) {
            if (cacheData.getExcelSteelMarks().contains(importVO.getSteelMark())) {
                throw exception(METER_IMPORT_STEEL_MARK_DUPLICATE, rowIndex + 1);
            } else {
                cacheData.getExcelSteelMarks().add(importVO.getSteelMark());
            }
        }

        // 水表编号重复校验
        if (StrUtil.isNotBlank(importVO.getSealNumber())) {
            if (cacheData.getExcelSealNumbers().contains(importVO.getSealNumber())) {
                throw exception(METER_IMPORT_SEAL_NUMBER_DUPLICATE, rowIndex + 1);
            } else {
                cacheData.getExcelSealNumbers().add(importVO.getSealNumber());
            }
        }

        // 条形码重复校验
        if (StrUtil.isNotBlank(importVO.getBarCode())) {
            if (cacheData.getExcelBarCodes().contains(importVO.getBarCode())) {
                throw exception(METER_IMPORT_BAR_CODE_DUPLICATE, rowIndex + 1);
            } else {
                cacheData.getExcelBarCodes().add(importVO.getBarCode());
            }
        }

        // 强检编号重复校验
        if (StrUtil.isNotBlank(importVO.getCheckCode())) {
            if (cacheData.getExcelCheckCodes().contains(importVO.getCheckCode())) {
                throw exception(METER_IMPORT_CHECK_CODE_DUPLICATE, rowIndex + 1);
            } else {
                cacheData.getExcelCheckCodes().add(importVO.getCheckCode());
            }
        }
    }

    /**
     * 数据库唯一性校验
     */
    private void validateUniqueInDatabase(MeterImportVO importVO, ImportCacheData cacheData,
            List<String> errorMessages) {
        // 钢印号数据库唯一性校验
        if (StrUtil.isNotBlank(importVO.getSteelMark())
                && cacheData.getExistingSteelMarks().contains(importVO.getSteelMark())) {
            errorMessages.add(StrUtil.format(METER_IMPORT_STEEL_MARK_EXISTS_IN_DB, importVO.getSteelMark()));
        }

        // 条形码数据库唯一性校验
        if (StrUtil.isNotBlank(importVO.getBarCode())
                && cacheData.getExistingBarCodes().contains(importVO.getBarCode())) {
            errorMessages.add(StrUtil.format(METER_IMPORT_BAR_CODE_EXISTS_IN_DB, importVO.getBarCode()));
        }


    }

    /**
     * 基础数据存在性校验
     * 
     * 校验逻辑：
     * 1. 水表厂家、水表型号、水表口径、水表量程：存在性 + 启用状态校验（必填）
     * 2. 水表类型：存在性校验（可选，有传值才校验）
     */
    private void validateBaseDataExists(MeterImportVO importVO, ImportCacheData cacheData, List<String> errorMessages) {
        // 厂家存在性+启用状态校验（必填）
        if (StrUtil.isNotBlank(importVO.getMakerName())
                && !cacheData.getMakerNameMap().containsKey(importVO.getMakerName())) {
            errorMessages.add(StrUtil.format(METER_IMPORT_MAKER_NOT_EXISTS_MSG, importVO.getMakerName()));
        }

        // 型号存在性+启用状态校验（必填）
        if (StrUtil.isNotBlank(importVO.getModelName())
                && !cacheData.getModelNameMap().containsKey(importVO.getModelName())) {
            errorMessages.add(StrUtil.format(METER_IMPORT_MODEL_NOT_EXISTS_MSG, importVO.getModelName()));
        }

        // 口径存在性+启用状态校验（必填）
        if (StrUtil.isNotBlank(importVO.getCaliberName())
                && !cacheData.getCaliberNameMap().containsKey(importVO.getCaliberName())) {
            errorMessages.add(StrUtil.format(METER_IMPORT_CALIBER_NOT_EXISTS_MSG, importVO.getCaliberName()));
        }

        // 量程存在性+启用状态校验（必填）
        if (StrUtil.isNotBlank(importVO.getRangeName())
                && !cacheData.getRangeNameMap().containsKey(importVO.getRangeName())) {
            errorMessages.add(StrUtil.format(METER_IMPORT_RANGE_NOT_EXISTS_MSG, importVO.getRangeName()));
        }

        // 水表类型存在性校验（可选，有传值才校验）
        if (StrUtil.isNotBlank(importVO.getTypeStr())) {
            // 使用 DictFrameworkUtils 手动转换
            String typeValue = DictFrameworkUtils.parseDictDataValue(DictTypeConstants.METER_OTH_TYPE, importVO.getTypeStr());
            if (typeValue == null) {
                // 转换失败，添加错误信息，并且可以显示用户输入的原始值
                errorMessages.add(StrUtil.format(METER_IMPORT_TYPE_NOT_EXISTS_MSG, importVO.getTypeStr()));
            }
            // 注意：这里不需要设置转换后的值，在 buildMeterInfo 中处理
        }
    }

    /**
     * 构建MeterInfo对象（包含字典转换）
     */
    private MeterImportRespVO.MeterInfo buildMeterInfo(MeterImportVO importVO, ImportCacheData cacheData) {
        MeterImportRespVO.MeterInfo meterInfo = new MeterImportRespVO.MeterInfo();

        // 基本字段映射
        meterInfo.setSteelMark(importVO.getSteelMark());
        meterInfo.setSealNumber(importVO.getSealNumber());
        meterInfo.setBarCode(importVO.getBarCode());
        meterInfo.setCheckCode(importVO.getCheckCode());
        meterInfo.setMakeDate(importVO.getMakeDate());
        meterInfo.setCheckDate(importVO.getCheckDate());
        meterInfo.setCollectCode(importVO.getCollectCode());

        // 数值字段转换
        if (importVO.getGpsx() != null) {
            meterInfo.setGpsx(BigDecimal.valueOf(importVO.getGpsx()));
        }
        if (importVO.getGpsy() != null) {
            meterInfo.setGpsy(BigDecimal.valueOf(importVO.getGpsy()));
        }
        if (importVO.getLatitude() != null) {
            meterInfo.setLatitude(BigDecimal.valueOf(importVO.getLatitude()));
        }
        if (importVO.getLongitude() != null) {
            meterInfo.setLongitude(BigDecimal.valueOf(importVO.getLongitude()));
        }
        if (importVO.getHighly() != null) {
            meterInfo.setHighly(BigDecimal.valueOf(importVO.getHighly()));
        }

        meterInfo.setImei(importVO.getImei());
        meterInfo.setImsi(importVO.getImsi());
        meterInfo.setModuleCode(importVO.getModuleCode());
        meterInfo.setNfcCode(importVO.getNfcCode());
        meterInfo.setQrCode(importVO.getQrCode());
        meterInfo.setMeasureNo(importVO.getMeasureNo());
        meterInfo.setRemark(importVO.getRemark());

        // 类型字段和字典转换
        if (StrUtil.isNotBlank(importVO.getTypeStr())) {
            // 手动进行字典转换
            String typeValue = DictFrameworkUtils.parseDictDataValue(DictTypeConstants.METER_OTH_TYPE, importVO.getTypeStr());
            if (typeValue != null) {
                // 转换成功
                meterInfo.setType(Integer.valueOf(typeValue));
                // 同时设置 typeName（用于显示）
                meterInfo.setTypeName(importVO.getTypeStr()); // 原始标签作为显示名称
            }
            // 如果转换失败，type 保持为 null，但不影响其他字段的构建
        }

        // 关联对象信息
        if (StrUtil.isNotBlank(importVO.getMakerName())) {
            MeterMakerDO maker = cacheData.getMakerNameMap().get(importVO.getMakerName());
            if (maker != null) {
                MeterImportRespVO.MakerInfo makerInfo = new MeterImportRespVO.MakerInfo();
                makerInfo.setId(maker.getId());
                makerInfo.setCode(maker.getCode());
                makerInfo.setName(maker.getName());
                meterInfo.setMaker(makerInfo);
            }
        }

        if (StrUtil.isNotBlank(importVO.getModelName())) {
            MeterModelDO model = cacheData.getModelNameMap().get(importVO.getModelName());
            if (model != null) {
                MeterImportRespVO.ModelInfo modelInfo = new MeterImportRespVO.ModelInfo();
                modelInfo.setId(model.getId());
                modelInfo.setCode(model.getCode());
                modelInfo.setName(model.getName());
                meterInfo.setModel(modelInfo);
            }
        }

        if (StrUtil.isNotBlank(importVO.getCaliberName())) {
            MeterCaliberDO caliber = cacheData.getCaliberNameMap().get(importVO.getCaliberName());
            if (caliber != null) {
                MeterImportRespVO.CaliberInfo caliberInfo = new MeterImportRespVO.CaliberInfo();
                caliberInfo.setId(caliber.getId());
                caliberInfo.setCode(caliber.getCode());
                caliberInfo.setName(caliber.getName());
                meterInfo.setCaliber(caliberInfo);
            }
        }

        if (StrUtil.isNotBlank(importVO.getRangeName())) {
            MeterRangeDO range = cacheData.getRangeNameMap().get(importVO.getRangeName());
            if (range != null) {
                MeterImportRespVO.RangeInfo rangeInfo = new MeterImportRespVO.RangeInfo();
                rangeInfo.setId(range.getId());
                rangeInfo.setCode(range.getCode());
                rangeInfo.setName(range.getName());
                meterInfo.setRange(rangeInfo);
            }
        }

        return meterInfo;
    }

    /**
     * 校验水表出库导入数据
     * 
     * 功能说明：
     * - 校验必填字段：钢印号、水表编号
     * - 校验钢印号是否存在于数据库
     * - Excel内重复性校验
     * - 仅校验不出库：返回详细的校验结果供前端处理
     */
    @Override
    public MeterImportRespVO checkMeterOutImportList(List<MeterOutImportVO> importList) {
        // 1. 前置校验
        if (CollUtil.isEmpty(importList)) {
            throw exception(METER_IMPORT_DATA_EMPTY);
        }

        // 2. 预加载水表数据到内存（性能优化关键）
        OutImportCacheData cacheData = preloadMeterDataForOut(importList);

        // 3. 逐条校验并收集结果
        List<MeterImportRespVO.MeterCheckItem> checkItems = new ArrayList<>();
        for (int i = 0; i < importList.size(); i++) {
            MeterOutImportVO importVO = importList.get(i);
            MeterImportRespVO.MeterCheckItem checkItem = validateSingleMeterOut(importVO, i, cacheData);
            checkItems.add(checkItem);
        }

        // 4. 统计校验结果
        MeterImportRespVO.CheckResult checkResult = buildCheckResult(checkItems);

        // 5. 构建返回结果（仅校验，不出库）
        return MeterImportRespVO.builder()
                .checkResult(checkResult)
                .meterCheckList(checkItems)
                .build();
    }

    /**
     * 预加载水表数据到内存（出库校验专用）
     */
    private OutImportCacheData preloadMeterDataForOut(List<MeterOutImportVO> importList) {
        OutImportCacheData cacheData = new OutImportCacheData();

        // 收集所有钢印号
        Set<String> steelMarks = importList.stream()
                .map(MeterOutImportVO::getSteelMark)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        if (CollUtil.isNotEmpty(steelMarks)) {
            // 批量查询数据库中的水表信息
            List<MeterDO> meterList = meterMapper.selectList(new LambdaQueryWrapperX<MeterDO>()
                    .in(MeterDO::getSteelMark, steelMarks));

            // 构建钢印号 -> 水表信息的Map
            cacheData.setSteelMarkToMeterMap(meterList.stream()
                    .collect(Collectors.toMap(MeterDO::getSteelMark, meter -> meter)));

            // 预加载基础数据到内存（复用现有方法）
            preloadBaseDataForOut(meterList, cacheData);
        }

        return cacheData;
    }

    /**
     * 预加载基础数据到内存（出库校验专用）
     * 复用现有的buildXxxCodeMap方法
     */
    private void preloadBaseDataForOut(List<MeterDO> meterList, OutImportCacheData cacheData) {
        // 收集所有需要查询的代码
        Set<String> makerCodes = meterList.stream()
                .map(MeterDO::getMakerCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        Set<String> modelCodes = meterList.stream()
                .map(MeterDO::getModelCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        Set<String> caliberCodes = meterList.stream()
                .map(MeterDO::getCaliberCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        Set<String> rangeCodes = meterList.stream()
                .map(MeterDO::getRangeCode)
                .filter(StrUtil::isNotBlank)
                .collect(Collectors.toSet());

        // 复用现有的构建方法（规范做法）
        cacheData.setMakerCodeMap(buildMakerCodeMap(makerCodes));
        cacheData.setModelCodeMap(buildModelCodeMap(modelCodes));
        cacheData.setCaliberCodeMap(buildCaliberCodeMap(caliberCodes));
        cacheData.setRangeCodeMap(buildRangeCodeMap(rangeCodes));

        // 预加载字典数据
        Set<String> typeValues = meterList.stream()
                .map(MeterDO::getType)
                .filter(Objects::nonNull)
                .map(String::valueOf)
                .collect(Collectors.toSet());

        if (CollUtil.isNotEmpty(typeValues)) {
            for (String typeValue : typeValues) {
                String typeName = DictFrameworkUtils.parseDictDataLabel(DictTypeConstants.METER_OTH_TYPE, typeValue);
                if (StrUtil.isNotBlank(typeName)) {
                    cacheData.getDictTypeNameMap().put(typeValue, typeName);
                }
            }
        }
    }

    /**
     * 校验单条水表出库数据
     */
    private MeterImportRespVO.MeterCheckItem validateSingleMeterOut(MeterOutImportVO importVO, int rowIndex,
            OutImportCacheData cacheData) {
        List<String> errorMessages = new ArrayList<>();

        // 1. 必填字段校验
        validateRequiredFieldsForOut(importVO, errorMessages);

        // 2. Excel内重复性校验
        validateDuplicateInExcelForOut(importVO, rowIndex, cacheData);

        // 3. 水表存在性校验
        MeterDO existingMeter = validateMeterExistsForOut(importVO, cacheData, errorMessages);

        // 4. 构建MeterInfo（基于查找到的水表信息）
        MeterImportRespVO.MeterInfo meterInfo = buildMeterInfoForOut(importVO, existingMeter, cacheData);

        return MeterImportRespVO.MeterCheckItem.builder()
                .isValid(errorMessages.isEmpty())
                .errorMessages(errorMessages)
                .meterInfo(meterInfo)
                .build();
    }

    /**
     * 必填字段校验（出库专用）
     * 必填字段：钢印号、水表编号
     */
    private void validateRequiredFieldsForOut(MeterOutImportVO importVO, List<String> errorMessages) {
        if (StrUtil.isBlank(importVO.getSteelMark())) {
            errorMessages.add(METER_OUT_IMPORT_STEEL_MARK_REQUIRED);
        }
        if (StrUtil.isBlank(importVO.getSealNumber())) {
            errorMessages.add(METER_OUT_IMPORT_SEAL_NUMBER_REQUIRED);
        }
    }

    /**
     * Excel内重复性校验（出库专用）
     * 注意：发现重复时立即抛异常，快速失败
     */
    private void validateDuplicateInExcelForOut(MeterOutImportVO importVO, int rowIndex,
            OutImportCacheData cacheData) {
        // 钢印号重复校验
        if (StrUtil.isNotBlank(importVO.getSteelMark())) {
            if (cacheData.getExcelSteelMarks().contains(importVO.getSteelMark())) {
                throw exception(METER_IMPORT_STEEL_MARK_DUPLICATE, rowIndex + 1);
            } else {
                cacheData.getExcelSteelMarks().add(importVO.getSteelMark());
            }
        }

        // 水表编号重复校验
//        if (StrUtil.isNotBlank(importVO.getSealNumber())) {
//            if (cacheData.getExcelSealNumbers().contains(importVO.getSealNumber())) {
//                throw exception(METER_IMPORT_SEAL_NUMBER_DUPLICATE, rowIndex + 1);
//            } else {
//                cacheData.getExcelSealNumbers().add(importVO.getSealNumber());
//            }
//        }
    }

    /**
     * 水表存在性校验（出库专用）
     */
    private MeterDO validateMeterExistsForOut(MeterOutImportVO importVO, OutImportCacheData cacheData, 
            List<String> errorMessages) {
        
        if (StrUtil.isBlank(importVO.getSteelMark())) {
            return null; // 钢印号为空的情况在必填字段校验中已经处理
        }

        // 根据钢印号查找水表
        MeterDO existingMeter = cacheData.getSteelMarkToMeterMap().get(importVO.getSteelMark());
        
        if (existingMeter == null) {
            // 钢印号不存在
            errorMessages.add(StrUtil.format(METER_OUT_IMPORT_STEEL_MARK_NOT_EXISTS, importVO.getSteelMark()));
            return null;
        }

        //校验该钢印号是否有水表编号
        if (StrUtil.isEmpty(existingMeter.getSealNumber())){
            errorMessages.add(METER_OUT_IMPORT_MISSING_SEAL_NUMBER);
        }

        // 校验水表编号是否匹配
//        if (StrUtil.isNotBlank(importVO.getSealNumber()) &&
//            !StrUtil.equals(importVO.getSealNumber(), existingMeter.getSealNumber())) {
//            errorMessages.add(StrUtil.format(METER_OUT_IMPORT_SEAL_NUMBER_NOT_MATCH,
//                importVO.getSteelMark(),
//                existingMeter.getSealNumber(),
//                importVO.getSealNumber()));
//        }

        return existingMeter;
    }

    /**
     * 构建水表信息（出库专用）
     * 参考现有的buildMeterInfo方法结构，复用相同的逻辑
     */
    private MeterImportRespVO.MeterInfo buildMeterInfoForOut(MeterOutImportVO importVO, MeterDO existingMeter, 
            OutImportCacheData cacheData) {
        MeterImportRespVO.MeterInfo meterInfo = new MeterImportRespVO.MeterInfo();
        
        // 如果找到了对应的水表，补充数据库中的完整信息
        if (existingMeter != null) {
            // 基本字段映射（参考buildMeterInfo方法）
            meterInfo.setSteelMark(existingMeter.getSteelMark());
            meterInfo.setSealNumber(existingMeter.getSealNumber());
            meterInfo.setType(existingMeter.getType() != null ? existingMeter.getType().intValue() : null);
            meterInfo.setBarCode(existingMeter.getBarCode());
            meterInfo.setCheckCode(existingMeter.getCheckCode());
            meterInfo.setMakeDate(existingMeter.getMakeDate());
            meterInfo.setCheckDate(existingMeter.getCheckDate());
            meterInfo.setCollectCode(existingMeter.getCollectCode());
            meterInfo.setGpsx(existingMeter.getGpsx());
            meterInfo.setGpsy(existingMeter.getGpsy());
            meterInfo.setLatitude(existingMeter.getLatitude());
            meterInfo.setLongitude(existingMeter.getLongitude());
            meterInfo.setHighly(existingMeter.getHighly());
            meterInfo.setImei(existingMeter.getImei());
            meterInfo.setImsi(existingMeter.getImsi());
            meterInfo.setModuleCode(existingMeter.getModuleCode());
            meterInfo.setNfcCode(existingMeter.getNfcCode());
            meterInfo.setQrCode(existingMeter.getQrCode());
            meterInfo.setMeasureNo(existingMeter.getMeasureNo());
            meterInfo.setRemark(existingMeter.getRemark());
            meterInfo.setId(existingMeter.getId());
            
            // 类型字段和字典转换
            if (existingMeter.getType() != null) {
                String typeValue = String.valueOf(existingMeter.getType());
                if (cacheData != null && cacheData.getDictTypeNameMap().containsKey(typeValue)) {
                    meterInfo.setTypeName(cacheData.getDictTypeNameMap().get(typeValue));
                } else {
                    meterInfo.setTypeName(DictFrameworkUtils.parseDictDataLabel(
                            DictTypeConstants.METER_OTH_TYPE, typeValue));
                }
            }

            // 关联对象信息组装（复用现有模式）
            buildMakerInfoForOut(meterInfo, existingMeter, cacheData);
            buildModelInfoForOut(meterInfo, existingMeter, cacheData);
            buildCaliberInfoForOut(meterInfo, existingMeter, cacheData);
            buildRangeInfoForOut(meterInfo, existingMeter, cacheData);
        }

        return meterInfo;
    }

    /**
     * 构建厂家信息（出库专用）
     */
    private void buildMakerInfoForOut(MeterImportRespVO.MeterInfo meterInfo, MeterDO existingMeter, OutImportCacheData cacheData) {
        if (StrUtil.isNotBlank(existingMeter.getMakerCode()) && cacheData != null) {
            MeterMakerDO maker = cacheData.getMakerCodeMap().get(existingMeter.getMakerCode());
            if (maker != null) {
                MeterImportRespVO.MakerInfo makerInfo = new MeterImportRespVO.MakerInfo();
                makerInfo.setId(maker.getId());
                makerInfo.setCode(maker.getCode());
                makerInfo.setName(maker.getName());
                meterInfo.setMaker(makerInfo);
            }
        }
    }

    /**
     * 构建型号信息（出库专用）
     */
    private void buildModelInfoForOut(MeterImportRespVO.MeterInfo meterInfo, MeterDO existingMeter, OutImportCacheData cacheData) {
        if (StrUtil.isNotBlank(existingMeter.getModelCode()) && cacheData != null) {
            MeterModelDO model = cacheData.getModelCodeMap().get(existingMeter.getModelCode());
            if (model != null) {
                MeterImportRespVO.ModelInfo modelInfo = new MeterImportRespVO.ModelInfo();
                modelInfo.setId(model.getId());
                modelInfo.setCode(model.getCode());
                modelInfo.setName(model.getName());
                meterInfo.setModel(modelInfo);
            }
        }
    }

    /**
     * 构建口径信息（出库专用）
     */
    private void buildCaliberInfoForOut(MeterImportRespVO.MeterInfo meterInfo, MeterDO existingMeter, OutImportCacheData cacheData) {
        if (StrUtil.isNotBlank(existingMeter.getCaliberCode()) && cacheData != null) {
            MeterCaliberDO caliber = cacheData.getCaliberCodeMap().get(existingMeter.getCaliberCode());
            if (caliber != null) {
                MeterImportRespVO.CaliberInfo caliberInfo = new MeterImportRespVO.CaliberInfo();
                caliberInfo.setId(caliber.getId());
                caliberInfo.setCode(caliber.getCode());
                caliberInfo.setName(caliber.getName());
                meterInfo.setCaliber(caliberInfo);
            }
        }
    }

    /**
     * 构建量程信息（出库专用）
     */
    private void buildRangeInfoForOut(MeterImportRespVO.MeterInfo meterInfo, MeterDO existingMeter, OutImportCacheData cacheData) {
        if (StrUtil.isNotBlank(existingMeter.getRangeCode()) && cacheData != null) {
            MeterRangeDO range = cacheData.getRangeCodeMap().get(existingMeter.getRangeCode());
            if (range != null) {
                MeterImportRespVO.RangeInfo rangeInfo = new MeterImportRespVO.RangeInfo();
                rangeInfo.setId(range.getId());
                rangeInfo.setCode(range.getCode());
                rangeInfo.setName(range.getName());
                meterInfo.setRange(rangeInfo);
            }
        }
    }

    /**
     * 出库导入缓存数据
     */
    @Data
    private static class OutImportCacheData {
        // 钢印号 -> 水表信息
        private Map<String, MeterDO> steelMarkToMeterMap = new HashMap<>();

        // 厂家代码 -> 厂家信息
        private Map<String, MeterMakerDO> makerCodeMap = new HashMap<>();

        // 型号代码 -> 型号信息
        private Map<String, MeterModelDO> modelCodeMap = new HashMap<>();

        // 口径代码 -> 口径信息
        private Map<String, MeterCaliberDO> caliberCodeMap = new HashMap<>();

        // 量程代码 -> 量程信息
        private Map<String, MeterRangeDO> rangeCodeMap = new HashMap<>();

        // 字典值 -> 字典标签
        private Map<String, String> dictTypeNameMap = new HashMap<>();

        // Excel内去重Set
        private Set<String> excelSteelMarks = new HashSet<>();
        private Set<String> excelSealNumbers = new HashSet<>();
    }

    /**
     * 统计校验结果
     */
    private MeterImportRespVO.CheckResult buildCheckResult(List<MeterImportRespVO.MeterCheckItem> checkItems) {
        int totalCount = checkItems.size();
        int successCount = (int) checkItems.stream().filter(MeterImportRespVO.MeterCheckItem::getIsValid).count();
        int failCount = totalCount - successCount;
        boolean isAllValid = failCount == 0;

        MeterImportRespVO.CheckResult checkResult = new MeterImportRespVO.CheckResult();
        checkResult.setTotalCount(totalCount);
        checkResult.setSuccessCount(successCount);
        checkResult.setFailCount(failCount);
        checkResult.setIsAllValid(isAllValid);

        return checkResult;
    }

    /**
     * 导入缓存数据
     */
    @Data
    private static class ImportCacheData {
        // 厂家名称 -> 厂家信息
        private Map<String, MeterMakerDO> makerNameMap = new HashMap<>();

        // 型号名称 -> 型号信息
        private Map<String, MeterModelDO> modelNameMap = new HashMap<>();

        // 口径名称 -> 口径信息
        private Map<String, MeterCaliberDO> caliberNameMap = new HashMap<>();

        // 量程名称 -> 量程信息
        private Map<String, MeterRangeDO> rangeNameMap = new HashMap<>();

        // 字典值 -> 字典标签
        private Map<String, String> dictTypeNameMap = new HashMap<>();

        // Excel内去重Set
        private Set<String> excelSteelMarks = new HashSet<>();
        private Set<String> excelSealNumbers = new HashSet<>();
        private Set<String> excelBarCodes = new HashSet<>();
        private Set<String> excelCheckCodes = new HashSet<>();

        // 数据库已存在的数据
        private Set<String> existingSteelMarks = new HashSet<>();
        private Set<String> existingBarCodes = new HashSet<>();
    }

    // ============== meterIn 相关辅助方法 ==============

    /**
     * 数据结构校验
     */
    private void validateMeterInBasicStructure(MeterInCreateVO createVO) {
        // 检查部门ID是否有效
        if (createVO.getDeptId() == null) {
            throw exception(METER_IN_DEPT_NOT_EXISTS);
        }

        deptApi.validateDeptExistsById(createVO.getDeptId()).checkError();

        // 检查水表明细列表
        if (CollUtil.isEmpty(createVO.getMeterList())) {
            throw exception(METER_IN_METER_LIST_EMPTY);
        }
    }

    /**
     * 执行入库业务校验（按需批量查询+Map缓存，高性能）
     */
    private void performMeterInValidation(List<MeterInCreateVO.MeterInItemCreateVO> meterList) {
        // 1. 收集所有需要校验的代码（按需收集，不查全量）
        Set<String> makerCodes = new HashSet<>();
        Set<String> modelCodes = new HashSet<>();
        Set<String> caliberCodes = new HashSet<>();
        Set<String> rangeCodes = new HashSet<>();
        
        for (MeterInCreateVO.MeterInItemCreateVO meterItem : meterList) {
            if (StrUtil.isNotBlank(meterItem.getMakerCode())) {
                makerCodes.add(meterItem.getMakerCode());
            }
            if (StrUtil.isNotBlank(meterItem.getModelCode())) {
                modelCodes.add(meterItem.getModelCode());
            }
            if (StrUtil.isNotBlank(meterItem.getCaliberCode())) {
                caliberCodes.add(meterItem.getCaliberCode());
            }
            if (StrUtil.isNotBlank(meterItem.getRangeCode())) {
                rangeCodes.add(meterItem.getRangeCode());
            }
        }

        // 2. 按需批量查询（只查询实际需要的代码，通过Service层）
        Map<String, MeterMakerDO> makerCodeMap = buildMakerCodeMap(makerCodes);
        Map<String, MeterModelDO> modelCodeMap = buildModelCodeMap(modelCodes);
        Map<String, MeterCaliberDO> caliberCodeMap = buildCaliberCodeMap(caliberCodes);
        Map<String, MeterRangeDO> rangeCodeMap = buildRangeCodeMap(rangeCodes);

        // 3. 收集唯一性校验数据
        Set<String> steelMarks = new HashSet<>();
        Set<String> barCodes = new HashSet<>();

        // 4. 逐条校验（使用Map缓存，O(1)复杂度）
        for (MeterInCreateVO.MeterInItemCreateVO meterItem : meterList) {
            // 基本规则校验
            validateMeterItemBasicRules(meterItem);
            
            // 存在性和启用状态校验（O(1)复杂度）
            validateExistenceAndStatus(meterItem, makerCodeMap, modelCodeMap, caliberCodeMap, rangeCodeMap);
            
            // 收集唯一性校验数据
            collectUniquenessData(meterItem, steelMarks, barCodes);
            
            // 水表类型校验（可选）
            validateTypeExists(meterItem.getType());
        }

        // 5. 批量校验唯一性
        validateUniquenessInDatabase(steelMarks, barCodes);
    }

    /**
     * 构建厂家代码Map缓存（按需查询）
     */
    private Map<String, MeterMakerDO> buildMakerCodeMap(Set<String> makerCodes) {
        if (CollUtil.isEmpty(makerCodes)) {
            return new HashMap<>();
        }
        List<MeterMakerDO> makers = meterMakerService.getEnabledMeterMakersByCodes(makerCodes);
        return makers.stream().collect(Collectors.toMap(MeterMakerDO::getCode, maker -> maker, (existing, replacement) -> existing));
    }

    /**
     * 构建型号代码Map缓存（按需查询）
     */
    private Map<String, MeterModelDO> buildModelCodeMap(Set<String> modelCodes) {
        if (CollUtil.isEmpty(modelCodes)) {
            return new HashMap<>();
        }
        List<MeterModelDO> models = meterModelService.getEnabledMeterModelsByCodes(modelCodes);
        return models.stream().collect(Collectors.toMap(MeterModelDO::getCode, model -> model, (existing, replacement) -> existing));
    }

    /**
     * 构建口径代码Map缓存（按需查询）
     */
    private Map<String, MeterCaliberDO> buildCaliberCodeMap(Set<String> caliberCodes) {
        if (CollUtil.isEmpty(caliberCodes)) {
            return new HashMap<>();
        }
        List<MeterCaliberDO> calibers = meterCaliberService.getEnabledMeterCalibersByCodes(caliberCodes);
        return calibers.stream().collect(Collectors.toMap(MeterCaliberDO::getCode, caliber -> caliber, (existing, replacement) -> existing));
    }

    /**
     * 构建量程代码Map缓存（按需查询）
     */
    private Map<String, MeterRangeDO> buildRangeCodeMap(Set<String> rangeCodes) {
        if (CollUtil.isEmpty(rangeCodes)) {
            return new HashMap<>();
        }
        List<MeterRangeDO> ranges = meterRangeService.getEnabledMeterRangesByCodes(rangeCodes);
        return ranges.stream().collect(Collectors.toMap(MeterRangeDO::getCode, range -> range, (existing, replacement) -> existing));
    }

    /**
     * 校验单个水表项的基本规则
     */
    private void validateMeterItemBasicRules(MeterInCreateVO.MeterInItemCreateVO meterItem) {
        if (meterItem.getQuantity() == null || meterItem.getQuantity() == 0) {
            // 单一数据：数量必须为0或null
            // 允许钢印号、条形码等唯一字段
        } else if (meterItem.getQuantity() > 0) {
            // 批量数据：钢印号、条形码必须为空
            if (StrUtil.isNotBlank(meterItem.getSteelMark())) {
                throw exception(METER_IN_BATCH_STEEL_MARK_NOT_EMPTY);
            }
            if (StrUtil.isNotBlank(meterItem.getBarCode())) {
                throw exception(METER_IN_BATCH_BAR_CODE_NOT_EMPTY);
            }
        } else {
            throw exception(METER_IN_QUANTITY_INVALID);
        }
    }

    /**
     * 校验存在性和启用状态（使用Map缓存，O(1)复杂度）
     */
    private void validateExistenceAndStatus(MeterInCreateVO.MeterInItemCreateVO meterItem,
                                            Map<String, MeterMakerDO> makerCodeMap,
                                            Map<String, MeterModelDO> modelCodeMap,
                                            Map<String, MeterCaliberDO> caliberCodeMap,
                                            Map<String, MeterRangeDO> rangeCodeMap) {
        // 校验厂家代码
        if (StrUtil.isNotBlank(meterItem.getMakerCode()) && !makerCodeMap.containsKey(meterItem.getMakerCode())) {
            throw exception(METER_IN_MAKER_CODE_NOT_EXISTS);
        }
        
        // 校验型号代码
        if (StrUtil.isNotBlank(meterItem.getModelCode()) && !modelCodeMap.containsKey(meterItem.getModelCode())) {
            throw exception(METER_IN_MODEL_CODE_NOT_EXISTS);
        }
        
        // 校验口径代码
        if (StrUtil.isNotBlank(meterItem.getCaliberCode()) && !caliberCodeMap.containsKey(meterItem.getCaliberCode())) {
            throw exception(METER_IN_CALIBER_CODE_NOT_EXISTS);
        }
        
        // 校验量程代码
        if (StrUtil.isNotBlank(meterItem.getRangeCode()) && !rangeCodeMap.containsKey(meterItem.getRangeCode())) {
            throw exception(METER_IN_RANGE_CODE_NOT_EXISTS);
        }
    }

    /**
     * 收集唯一性校验数据（仅限单一数据）
     */
    private void collectUniquenessData(MeterInCreateVO.MeterInItemCreateVO meterItem, 
                                       Set<String> steelMarks, Set<String> barCodes) {
        // 只有单一数据（quantity为0或null）才需要校验唯一性
        if (meterItem.getQuantity() == null || meterItem.getQuantity() == 0) {
            if (StrUtil.isNotBlank(meterItem.getSteelMark())) {
                steelMarks.add(meterItem.getSteelMark());
            }
            if (StrUtil.isNotBlank(meterItem.getBarCode())) {
                barCodes.add(meterItem.getBarCode());
            }
        }
    }

    /**
     * 校验水表类型存在性（可选）
     */
    private void validateTypeExists(Short type) {
        if (type == null) {
            return;
        }
        String typeValue = String.valueOf(type);
        String typeName = DictFrameworkUtils.parseDictDataLabel(DictTypeConstants.METER_OTH_TYPE, typeValue);
        if (typeName == null) {
            throw exception(METER_IN_TYPE_NOT_EXISTS);
        }
    }

    /**
     * 批量校验唯一性
     */
    private void validateUniquenessInDatabase(Set<String> steelMarks, Set<String> barCodes) {
        // 批量校验钢印号唯一性
        if (CollUtil.isNotEmpty(steelMarks)) {
            List<String> existingSteelMarks = meterMapper.selectExistingSteelMarks(steelMarks);
            if (CollUtil.isNotEmpty(existingSteelMarks)) {
                throw exception(METER_IN_STEEL_MARK_EXISTS);
            }
        }

        // 批量校验条形码唯一性
        if (CollUtil.isNotEmpty(barCodes)) {
            List<String> existingBarCodes = meterMapper.selectExistingBarCodes(barCodes);
            if (CollUtil.isNotEmpty(existingBarCodes)) {
                throw exception(METER_IN_BAR_CODE_EXISTS);
            }
        }
    }

    /**
     * 创建入库单记录
     */
    private Long createMeterInOutRecord(MeterInCreateVO createVO, String stockCode) {
        MeterInOutDO inOutRecord = MeterInOutDO.builder()
                .deptId(createVO.getDeptId())
                .payCode(createVO.getPayCode())
                .stockCode(stockCode)
                .stockType(createVO.getStockType())
                .operationUser(createVO.getOperationUser())
                .operationTime(createVO.getOperationTime())
                .remark(createVO.getRemark())
                .build();

        meterInOutMapper.insert(inOutRecord);
        return inOutRecord.getId();
    }

    /**
     * 批量插入水表信息和关联关系
     */
    private void batchInsertMeters(List<MeterInCreateVO.MeterInItemCreateVO> meterList, Long inOutId, Long deptId) {
        List<MeterDO> metersToInsert = new ArrayList<>();

        for (MeterInCreateVO.MeterInItemCreateVO meterItem : meterList) {
            if (meterItem.getQuantity() == null || meterItem.getQuantity() == 0) {
                // 单一数据：插入一条记录
                MeterDO meter = buildMeterDO(meterItem, deptId);
                metersToInsert.add(meter);
            } else {
                // 批量数据：根据数量插入多条记录
                for (int i = 0; i < meterItem.getQuantity(); i++) {
                    MeterDO meter = buildMeterDO(meterItem, deptId);
                    // 批量数据不设置钢印号、条形码等唯一字段
                    meter.setSteelMark(null);
                    meter.setBarCode(null);
                    metersToInsert.add(meter);
                }
            }
        }

        // 批量插入水表（使用BaseMapperX的高性能批量插入）
        if (CollUtil.isNotEmpty(metersToInsert)) {
            meterMapper.insertBatch(metersToInsert);
            
            // 插入成功后，收集所有水表ID并创建关联关系
            List<Long> meterIds = metersToInsert.stream()
                    .map(MeterDO::getId)
                    .filter(Objects::nonNull)
                    .toList();
            
            // 批量插入出入库单与水表的关联关系
            meterInOutRelMapper.insertBatchMeterInOutRefs(inOutId, meterIds);
            
            // 异步记录入库日志
            asyncCreateMeterInOutLogs(meterIds, inOutId, MeterLogEnum.METER_IN.getType(),"成功","批量入库");
        }
    }

    /**
     * 构建水表DO对象
     */
    private MeterDO buildMeterDO(MeterInCreateVO.MeterInItemCreateVO meterItem, Long deptId) {
        return MeterDO.builder()
                .deptId(deptId)
                .makerCode(meterItem.getMakerCode())
                .modelCode(meterItem.getModelCode())
                .caliberCode(meterItem.getCaliberCode())
                .rangeCode(meterItem.getRangeCode())
                .type(meterItem.getType())
                .steelMark(meterItem.getSteelMark())
                .sealNumber(meterItem.getSealNumber())
                .barCode(meterItem.getBarCode())
                .checkCode(meterItem.getCheckCode())
                .makeDate(meterItem.getMakeDate())
                .checkDate(meterItem.getCheckDate())
                .collectCode(meterItem.getCollectCode())
                .gpsx(meterItem.getGpsx())
                .gpsy(meterItem.getGpsy())
                .latitude(meterItem.getLatitude())
                .longitude(meterItem.getLongitude())
                .highly(meterItem.getHighly())
                .imei(meterItem.getImei())
                .imsi(meterItem.getImsi())
                .moduleCode(meterItem.getModuleCode())
                .nfcCode(meterItem.getNfcCode())
                .qrCode(meterItem.getQrCode())
                .measureNo(meterItem.getMeasureNo())
                .remark(meterItem.getRemark())
                .meterStatus(MWStateEnum.METER_IN.getType()) // 默认状态为1
                .build();
    }

    /**
     * 异步创建水表入库日志
     * 
     * @param meterIds 水表ID列表
     * @param inOutId 出入库单ID
     */
    @Async
    protected void asyncCreateMeterInOutLogs(List<Long> meterIds, Long inOutId,Short logType ,String result,String remark) {
        try {
            meterLogService.batchCreateMeterInOutLogs(meterIds, inOutId, logType, result, remark);
        } catch (Exception e) {
            // 日志记录失败不应该影响主业务流程，仅记录错误
            // 这里可以使用日志框架记录异常信息
            // 后续使用mq重试处理
        }
    }

    // ============== 入库单编号生成相关方法 ==============

    /**
     * 生成下一个入库单编号（基于数据库最新记录）
     * 格式：RK + YYYYMMDD + 批次（3位数字，当日递增）
     * 示例：RK20250730001, RK20250730002
     * 
     * 业务逻辑：
     * 1. 查询数据库最新的入库单记录
     * 2. 如果没有记录或记录不是今天的，从001开始
     * 3. 如果是今天的记录，在其基础上+1
     *
     * @return 新的入库单编号
     */
    private String generateNextStockInCode() {
        // 1. 查询最新的入库单记录
        MeterInOutDO latestRecord = meterInOutMapper.selectLatestStockIn();
        
        // 2. 获取今天的日期字符串
        String todayStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 3. 如果没有记录，从001开始
        if (latestRecord == null || StrUtil.isBlank(latestRecord.getStockCode())) {
            return String.format("RK%s001", todayStr);
        }
        
        // 4. 解析最新记录的编号
        String latestStockCode = latestRecord.getStockCode();
        StockCodeInfo codeInfo = parseStockCode(latestStockCode);
        
        // 5. 如果解析失败或不是今天的记录，从001开始
        if (codeInfo == null || !todayStr.equals(codeInfo.getDateStr())) {
            return String.format("RK%s001", todayStr);
        }
        
        // 6. 如果是今天的记录，序号+1
        int nextSequence = codeInfo.getSequence() + 1;
        String sequenceStr = String.format("%03d", nextSequence);
        
        return String.format("RK%s%s", todayStr, sequenceStr);
    }

    /**
     * 生成下一个出库单编号（基于数据库最新记录）
     * 格式：CK + YYYYMMDD + 批次（3位数字，当日递增）
     * 示例：CK20250730001, CK20250730002
     * 
     * 业务逻辑：
     * 1. 查询数据库最新的出库单记录
     * 2. 如果没有记录或记录不是今天的，从001开始
     * 3. 如果是今天的记录，在其基础上+1
     *
     * @return 新的出库单编号
     */
    private String generateNextStockOutCode() {
        // 1. 查询最新的出库单记录
        MeterInOutDO latestRecord = meterInOutMapper.selectLatestStockOut();
        
        // 2. 获取今天的日期字符串
        String todayStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        
        // 3. 如果没有记录，从001开始
        if (latestRecord == null || StrUtil.isBlank(latestRecord.getStockCode())) {
            return String.format("CK%s001", todayStr);
        }
        
        // 4. 解析最新记录的编号
        String latestStockCode = latestRecord.getStockCode();
        StockCodeInfo codeInfo = parseStockCode(latestStockCode);
        
        // 5. 如果解析失败或不是今天的记录，从001开始
        if (codeInfo == null || !todayStr.equals(codeInfo.getDateStr())) {
            return String.format("CK%s001", todayStr);
        }
        
        // 6. 如果是今天的记录，序号+1
        int nextSequence = codeInfo.getSequence() + 1;
        String sequenceStr = String.format("%03d", nextSequence);
        
        return String.format("CK%s%s", todayStr, sequenceStr);
    }

    /**
     * 解析库存单编号（支持入库RK和出库CK）
     * 
     * @param stockCode 库存单编号，格式：RK/CK + YYYYMMDD + XXX
     * @return 解析结果，如果格式不正确则返回null
     */
    private StockCodeInfo parseStockCode(String stockCode) {
        if (StrUtil.isBlank(stockCode) || stockCode.length() != 13 || 
            (!stockCode.startsWith("RK") && !stockCode.startsWith("CK"))) {
            return null;
        }
        
        try {
            // 提取日期部分（第3-10位）
            String dateStr = stockCode.substring(2, 10);
            
            // 提取序号部分（第11-13位）
            String sequenceStr = stockCode.substring(10, 13);
            int sequence = Integer.parseInt(sequenceStr);
            
            // 验证日期格式是否正确
            LocalDateTime.parse(dateStr + "000000", DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
            
            return new StockCodeInfo(dateStr, sequence);
        } catch (Exception e) {
            // 解析失败，返回null
            return null;
        }
    }

    /**
     * 入库单编号信息
     */
    @Data
    private static class StockCodeInfo {
        private String dateStr;    // 日期字符串（yyyyMMdd）
        private int sequence;      // 序号

        public StockCodeInfo(String dateStr, int sequence) {
            this.dateStr = dateStr;
            this.sequence = sequence;
        }
    }

    // ============== 水表出库相关方法 ==============

    /**
     * 校验出库基本参数结构
     */
    private void validateMeterOutBasicStructure(MeterOutCreateVO createVO) {
        if (CollUtil.isEmpty(createVO.getMeterIds())) {
            throw exception(METER_OUT_METER_IDS_EMPTY);
        }

        // 调库时必须指定目标仓库
        if (Boolean.TRUE.equals(createVO.getIsAdjustDept()) && createVO.getAdjustDeptId() == null) {
            throw exception(METER_OUT_ADJUST_DEPT_REQUIRED);
        }
    }

    /**
     * 出库业务数据校验
     * @param createVO 出库VO
     * @return 校验通过的水表列表
     */
    private List<MeterDO> performMeterOutValidation(MeterOutCreateVO createVO) {
        List<Long> meterIds = createVO.getMeterIds();
        
        // 批量查询水表信息
        List<MeterDO> meters = meterMapper.selectBatchIds(meterIds);
        
        // 检查是否所有水表都存在
        if (meters.size() != meterIds.size()) {
            Set<Long> foundIds = meters.stream().map(MeterDO::getId).collect(Collectors.toSet());
            List<Long> notFoundIds = meterIds.stream()
                    .filter(id -> !foundIds.contains(id))
                    .toList();
            throw exception(METER_OUT_METER_NOT_EXISTS, notFoundIds.get(0));
        }

        // 逐一校验每个水表
        for (MeterDO meter : meters) {
            validateSingleMeterForOut(meter);
        }

        return meters;
    }

    /**
     * 校验单个水表出库条件
     */
    private void validateSingleMeterForOut(MeterDO meter) {
        // 校验钢印号不能为空
        if (StrUtil.isBlank(meter.getSteelMark())) {
            throw exception(METER_OUT_METER_STEEL_MARK_EMPTY, meter.getId());
        }

        // 校验水表编号不能为空
        if (StrUtil.isBlank(meter.getSealNumber())) {
            throw exception(METER_OUT_METER_SEAL_NUMBER_EMPTY, meter.getId());
        }

        // 校验水表状态必须为入库状态（1）
        if (meter.getMeterStatus() == null || meter.getMeterStatus() != MWStateEnum.METER_IN.getType()) {
            throw exception(METER_OUT_METER_STATUS_INVALID, meter.getId(), meter.getMeterStatus());
        }
    }

    /**
     * 调库特殊校验
     */
    private void validateAdjustDeptOperation(MeterOutCreateVO createVO, List<MeterDO> metersToOut) {
        Long targetDeptId = createVO.getAdjustDeptId();
        
        // 收集需要调库的水表钢印号
        Set<String> steelMarks = metersToOut.stream()
                .map(MeterDO::getSteelMark)
                .collect(Collectors.toSet());

        // 查询目标仓库是否存在相同钢印号的水表
        List<MeterDO> existingMeters = meterMapper.selectList(new LambdaQueryWrapperX<MeterDO>()
                .eq(MeterDO::getDeptId, targetDeptId)
                .in(MeterDO::getSteelMark, steelMarks));

        if (CollUtil.isNotEmpty(existingMeters)) {
            String duplicateSteelMark = existingMeters.get(0).getSteelMark();
            throw exception(METER_OUT_ADJUST_DEPT_DUPLICATE_STEEL_MARK, duplicateSteelMark);
        }
    }

    /**
     * 执行出库操作
     */
    private Long executeOutOperation(MeterOutCreateVO createVO, List<MeterDO> metersToOut) {
        // 1. 处理出库单记录（支持补录场景）
        Long outId = handleMeterOutRecord(createVO);

        // 2. 更新水表状态为出库状态（2）
        List<Long> meterIds = metersToOut.stream().map(MeterDO::getId).toList();
        updateMeterStatusToOut(meterIds);

        // 3. 创建出库关联记录
        meterInOutRelMapper.insertBatchMeterInOutRefs(outId, meterIds);

        // 4. 异步记录出库日志
        asyncCreateMeterInOutLogs(meterIds, outId, MeterLogEnum.METER_OUT.getType(), "成功", "批量出库");

        return outId;
    }

    /**
     * 处理出库单记录逻辑（支持补录场景）
     * @param createVO 创建出库单的VO
     * @return 出库单ID
     */
    private Long handleMeterOutRecord(MeterOutCreateVO createVO) {
        String inputStockCode = createVO.getStockCode();

        // 如果没有传出库单编号，生成新编号并创建记录
        if (StrUtil.isBlank(inputStockCode)) {
            String stockCode = generateNextStockOutCode();
            return createMeterOutRecord(createVO, stockCode);
        }

        // 检查传入的库存单编号是否已存在
        MeterInOutDO existingRecord = meterInOutMapper.selectOne(MeterInOutDO::getStockCode, inputStockCode);

        if (existingRecord != null) {
            // 库存单编号已存在，直接返回现有记录的ID（补录场景）
            return existingRecord.getId();
        } else {
            // 库存单编号不存在，使用传入的编号创建新记录
            return createMeterOutRecord(createVO, inputStockCode);
        }
    }

    /**
     * 创建出库单记录
     */
    private Long createMeterOutRecord(MeterOutCreateVO createVO, String stockCode) {
        MeterInOutDO outRecord = MeterInOutDO.builder()
                .deptId(createVO.getDeptId())
                .payCode(createVO.getPayCode())
                .stockCode(stockCode)
                .stockType(MWStateEnum.METER_OUT.getType()) // 2=出库
                .operationUser(createVO.getOperationUser())
                .operationTime(createVO.getOperationTime())
                .remark(createVO.getRemark())
                .build();

        meterInOutMapper.insert(outRecord);
        return outRecord.getId();
    }

    /**
     * 批量更新水表状态为出库状态
     */
    private void updateMeterStatusToOut(List<Long> meterIds) {
        if (CollUtil.isNotEmpty(meterIds)) {
            for (Long meterId : meterIds) {
                MeterDO updateMeter = MeterDO.builder()
                        .id(meterId)
                        .meterStatus(MWStateEnum.METER_OUT.getType()) // 2=出库状态
                        .build();
                meterMapper.updateById(updateMeter);
            }
        }
    }

    /**
     * 执行调库入库操作
     */
    private void executeAdjustInOperation(MeterOutCreateVO createVO, List<MeterDO> metersToOut, Long outId) {
        // 1. 生成入库单编号
        String inStockCode = generateNextStockInCode();

        // 2. 创建入库单记录
        Long inId = createAdjustInRecord(createVO, inStockCode);

        // 3. 更新水表信息（部门ID和状态）
        List<Long> meterIds = metersToOut.stream().map(MeterDO::getId).toList();
        updateMeterForAdjustIn(meterIds, createVO.getAdjustDeptId());

        // 4. 创建入库关联记录
        meterInOutRelMapper.insertBatchMeterInOutRefs(inId, meterIds);

        // 5. 异步记录入库日志
        asyncCreateMeterInOutLogs(meterIds, inId, MeterLogEnum.METER_IN.getType(), "成功", "调库入库");
    }

    /**
     * 创建调库入库单记录
     */
    private Long createAdjustInRecord(MeterOutCreateVO createVO, String stockCode) {
        MeterInOutDO inRecord = MeterInOutDO.builder()
                .deptId(createVO.getAdjustDeptId()) // 目标仓库
                .payCode(createVO.getPayCode())
                .stockCode(stockCode)
                .stockType(MWStateEnum.METER_IN.getType()) // 1=入库
                .operationUser(createVO.getOperationUser())
                .operationTime(createVO.getOperationTime())
                .remark("调库入库")
                .build();

        meterInOutMapper.insert(inRecord);
        return inRecord.getId();
    }

    /**
     * 更新水表信息用于调库入库
     */
    private void updateMeterForAdjustIn(List<Long> meterIds, Long targetDeptId) {
        if (CollUtil.isNotEmpty(meterIds)) {
            for (Long meterId : meterIds) {
                MeterDO updateMeter = MeterDO.builder()
                        .id(meterId)
                        .deptId(targetDeptId) // 更新到目标仓库
                        .meterStatus(MWStateEnum.METER_IN.getType()) // 1=入库状态
                        .build();
                meterMapper.updateById(updateMeter);
            }
        }
    }

}