package cn.com.emsoft.sw.business.controller.admin.meterinout.vo;

import cn.com.emsoft.sw.framework.common.pojo.SearchAndSortPageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.com.emsoft.sw.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 水表出/入库单明细分页 Request VO")
@Data
public class MeterInOutPageDetailsReqVO extends SearchAndSortPageParam {

    @Schema(description = "营业站点", example = "14736")
    private Long deptId;

    @Schema(description = "入/出库编号")
    private String stockCode;

    @Schema(description = "出/入库人，记入用户名")
    private String[] operationUser;

    @Schema(description = "钢印号", example = "a001")
    private String steelMark;

    @Schema(description = "强检编号", example = "d001")
    private String checkCode;

    @Schema(description = "水表厂家代码，关联水表厂家表")
    private String makerCode;

    @Schema(description = "水表型号代码，关联水表型号表")
    private String modelCode;

    @Schema(description = "水表口径代码，关联水表口径表")
    private String caliberCode;

    @Schema(description = "水表量程代码，关联水表量程表")
    private String rangeCode;

    @Schema(description = "水表分类/类型", example = "1")
    private Short type;

    @Schema(description = "采购编号")
    private String payCode;

    @Schema(description = "库存单类型，1=入库，2=出库", example = "1")
    @NotNull(message = "库存单类型不能为空,1=入库,2=出库")
    private Short stockType;

    @Schema(description = "出/入库时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] operationTime;

}