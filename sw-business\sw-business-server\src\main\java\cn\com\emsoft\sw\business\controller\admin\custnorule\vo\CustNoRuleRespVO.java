package cn.com.emsoft.sw.business.controller.admin.custnorule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 户号规则 Response VO")
@Data
@ExcelIgnoreUnannotated
public class CustNoRuleRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "30839")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "规则编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规则编码")
    private String code;

    @Schema(description = "最大值", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("最大值")
    private Integer seqNo;

    @Schema(description = "规则正则表达式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规则正则表达式")
    private String seqRegex;

    @Schema(description = "是否绑定站点：0-是，1-否")
    @ExcelProperty("是否绑定站点：0-是，1-否")
    private Short isBind;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "状态：0-是，1-否", example = "1")
    @ExcelProperty("状态：0-是，1-否")
    private Short status;

}