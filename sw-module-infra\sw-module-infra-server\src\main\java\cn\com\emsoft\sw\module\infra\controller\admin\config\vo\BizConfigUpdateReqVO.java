package cn.com.emsoft.sw.module.infra.controller.admin.config.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

@Schema(description = "管理后台 - 抄表参数&参数配置创建 Request VO")
@Data
public class BizConfigUpdateReqVO {

    @NotNull(message = "主键不能为空")
    private Long id;

    @Schema(description = "参数分组", requiredMode = Schema.RequiredMode.REQUIRED, example = "biz")
    @NotEmpty(message = "参数分组不能为空")
    @Size(max = 50, message = "参数名称不能超过 50 个字符")
    private String category;

    @Schema(description = "参数类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "biz")
    @NotNull(message = "参数类型不能为空，参考字典中的参数类型")
    private Integer type;

    @Schema(description = "参数名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "数据库名")
    @NotBlank(message = "参数名称不能为空")
    @Size(max = 100, message = "参数名称不能超过 100 个字符")
    private String name;

    @Schema(description = "参数键名", requiredMode = Schema.RequiredMode.REQUIRED, example = "yunai.db.username")
    @NotBlank(message = "参数键名长度不能为空")
    @Size(max = 100, message = "参数键名长度不能超过 100 个字符")
    private String configKey;

    @Schema(description = "参数键值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @NotBlank(message = "参数键值不能为空")
    @Size(max = 500, message = "参数键值长度不能超过 500 个字符")
    private String value;

    @Schema(description = "是否标红", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean red;

    @Schema(description = "是否编辑", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean edit;

    @Schema(description = "备注", example = "备注一下很帅气！")
    private String remark;

}
