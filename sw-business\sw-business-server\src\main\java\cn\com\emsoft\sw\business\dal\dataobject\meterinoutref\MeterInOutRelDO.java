package cn.com.emsoft.sw.business.dal.dataobject.meterinoutref;

import cn.com.emsoft.sw.framework.common.validation.GetterRef;
import cn.com.emsoft.sw.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 水表出/入库详情 DO
 *
 * <AUTHOR>
 */
@TableName("biz_meter_in_out_rel")
@KeySequence("biz_meter_in_out_rel_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@GetterRef
public class MeterInOutRelDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 水表出/入库单id，关联水表出入库单
     */
    private Long meterInOutId;
    /**
     * 水表id，关联水表信息表
     */
    private Long meterId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态：0-否，1-是
     */
    private Short status;


}