package cn.com.emsoft.sw.business.dal.dataobject.meterinout;

import cn.com.emsoft.sw.framework.common.validation.GetterRef;
import cn.com.emsoft.sw.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.com.emsoft.sw.framework.mybatis.core.dataobject.BaseDO;

/**
 * 水表出/入库单 DO
 *
 * <AUTHOR>
 */
@TableName("biz_meter_in_out")
@KeySequence("biz_meter_in_out_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@GetterRef
public class MeterInOutDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 营业站点
     */
    private Long deptId;
    /**
     * 采购单编号
     */
    private String payCode;
    /**
     * 库存单编号，RK/CK + 6位随机
     */
    private String stockCode;
    /**
     * 库存单类型，1=入库，2=出库
     */
    private Short stockType;
    /**
     * 出/入库人，记入用户名
     */
    private String operationUser;
    /**
     * 出/入库时间
     */
    private LocalDateTime operationTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态：0-否，1-是
     */
    private Short status;


}