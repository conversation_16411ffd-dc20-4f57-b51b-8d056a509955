package cn.com.emsoft.sw.business.dal.redis.pricetemplate;

import cn.hutool.core.util.StrUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

import java.util.concurrent.TimeUnit;

import static cn.com.emsoft.sw.business.dal.redis.RedisKeyConstants.PRICE_TEMPLATE_ADJUSTMENT_LOCK;
import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.*;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 水价调整分布式锁 Redis DAO
 * 
 * <AUTHOR>
 */
@Repository
@Slf4j
public class PriceTemplateAdjustmentLockRedisDAO {

    @Resource
    private RedissonClient redissonClient;
    
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 尝试获取调价锁
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @param timeoutMillis 锁超时时间（毫秒）
     * @return 获取成功返回true，失败返回false
     */
    public boolean tryAcquireLock(Long tenantId, Long userId, long timeoutMillis) {
        String lockKey = formatKey(tenantId);
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            // 尝试获取锁，不等待
            boolean acquired = lock.tryLock(0, timeoutMillis, TimeUnit.MILLISECONDS);
            if (acquired) {
                // 获取锁成功后，设置用户ID到Redis
                setLockUserInfo(tenantId, userId);
                log.info("[调价锁] 成功获取锁，租户ID: {}, 用户ID: {}", tenantId, userId);
            }
            return acquired;
        } catch (InterruptedException e) {
            log.error("[调价锁] 获取锁被中断，租户ID: {}, 用户ID: {}", tenantId, userId, e);
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 释放调价锁
     * 
     * @param tenantId 租户ID
     * @param userId 当前用户ID（用于验证权限）
     * @return 释放成功返回true
     */
    public boolean releaseLock(Long tenantId, Long userId) {
        String lockKey = formatKey(tenantId);
        
        try {
            // 检查是否是同一用户持有锁
            Long currentLockUserId = getLockUserInfo(tenantId);
            if (currentLockUserId == null) {
                log.warn("[调价锁] 锁不存在或已过期，租户ID: {}, 用户ID: {}", tenantId, userId);
                return true; // 锁不存在，认为释放成功
            }
            
            if (!userId.equals(currentLockUserId)) {
                log.warn("[调价锁] 无权释放锁，当前用户ID: {}, 锁持有者ID: {}, 租户ID: {}", 
                        userId, currentLockUserId, tenantId);
                throw exception(PRICE_TEMPLATE_ADJUSTMENT_LOCK_NOT_OWNED_BY_USER, currentLockUserId);
            }
            
            // 使用强制释放锁，支持跨线程释放
            RLock lock = redissonClient.getLock(lockKey);
            if (lock.isLocked()) {
                lock.forceUnlock();
                // 清除用户信息
                clearLockUserInfo(tenantId);
                log.info("[调价锁] 成功释放锁，租户ID: {}, 用户ID: {}", tenantId, userId);
                return true;
            }
            
            return true;
        } catch (Exception e) {
            log.error("[调价锁] 释放锁异常，租户ID: {}, 用户ID: {}", tenantId, userId, e);
            // 如果是业务异常，直接抛出
            if (e instanceof cn.com.emsoft.sw.framework.common.exception.ServiceException) {
                throw e;
            }
            return false;
        }
    }

    /**
     * 获取当前持有锁的用户信息
     * 
     * @param tenantId 租户ID
     * @return 用户ID，如果锁不存在返回null
     */
    public Long getLockUserInfo(Long tenantId) {
        String userKey = formatUserKey(tenantId);
        String userIdStr = stringRedisTemplate.opsForValue().get(userKey);
        if (StrUtil.isBlank(userIdStr)) {
            return null;
        }
        try {
            return Long.valueOf(userIdStr);
        } catch (NumberFormatException e) {
            log.warn("[调价锁] 用户ID格式错误，租户ID: {}, userIdStr: {}", tenantId, userIdStr);
            return null;
        }
    }

    /**
     * 检查锁是否存在
     * 
     * @param tenantId 租户ID
     * @return 锁是否存在
     */
    public boolean isLockExists(Long tenantId) {
        String lockKey = formatKey(tenantId);
        RLock lock = redissonClient.getLock(lockKey);
        return lock.isLocked();
    }

    /**
     * 强制释放锁（管理员操作）
     * 
     * @param tenantId 租户ID
     */
    public void forceReleaseLock(Long tenantId) {
        String lockKey = formatKey(tenantId);
        RLock lock = redissonClient.getLock(lockKey);
        
        try {
            if (lock.isLocked()) {
                lock.forceUnlock();
                clearLockUserInfo(tenantId);
                log.info("[调价锁] 强制释放锁成功，租户ID: {}", tenantId);
            }
        } catch (Exception e) {
            log.error("[调价锁] 强制释放锁失败，租户ID: {}", tenantId, e);
        }
    }

    /**
     * 设置锁的用户信息
     */
    private void setLockUserInfo(Long tenantId, Long userId) {
        String userKey = formatUserKey(tenantId);
        // 设置与锁相同的过期时间
        stringRedisTemplate.opsForValue().set(userKey, userId.toString(), 30, TimeUnit.MINUTES);
    }

    /**
     * 清除锁的用户信息
     */
    private void clearLockUserInfo(Long tenantId) {
        String userKey = formatUserKey(tenantId);
        stringRedisTemplate.delete(userKey);
    }

    /**
     * 格式化锁的Redis Key
     */
    private static String formatKey(Long tenantId) {
        return String.format(PRICE_TEMPLATE_ADJUSTMENT_LOCK, tenantId);
    }

    /**
     * 格式化用户信息的Redis Key
     */
    private static String formatUserKey(Long tenantId) {
        return String.format(PRICE_TEMPLATE_ADJUSTMENT_LOCK + ":user", tenantId);
    }
}