package cn.com.emsoft.sw.business.service.invoicetaxrate;

import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxrateCreateReqVO;
import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxratePageReqVO;
import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxrateRespVO;
import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxrateUpdateReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.invoicetaxrate.InvoiceTaxrateDO;
import cn.com.emsoft.sw.business.dal.mysql.companyaccount.CompanyAccountMapper;
import cn.com.emsoft.sw.business.dal.mysql.invoicetaxrate.InvoiceTaxrateMapper;
import cn.com.emsoft.sw.business.enums.DictTypeConstants;
import cn.com.emsoft.sw.framework.dict.core.DictFrameworkUtils;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;

import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;

import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.*;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 发票税率 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InvoiceTaxrateServiceImpl implements InvoiceTaxrateService {

    @Resource
    private InvoiceTaxrateMapper invoiceTaxrateMapper;

    @Resource
    private CompanyAccountMapper companyAccountMapper;

    @Override
    public Long createInvoiceTaxrate(InvoiceTaxrateCreateReqVO createReqVO) {
        //校验该水司账户下的编码，名称，商品编码唯一
        validateNameAndCodeAndProductCodeUniqueOnSameAccount(null,createReqVO.getName(),createReqVO.getCode(),
                createReqVO.getProductCode(),createReqVO.getAccountId());
        // 插入
        InvoiceTaxrateDO invoiceTaxrate = BeanUtils.toBean(createReqVO, InvoiceTaxrateDO.class);
        invoiceTaxrateMapper.insert(invoiceTaxrate);
        // 返回
        return invoiceTaxrate.getId();
    }

    @Override
    public void updateInvoiceTaxrate(InvoiceTaxrateUpdateReqVO updateReqVO) {
        // 校验存在
        validateInvoiceTaxrateExists(updateReqVO.getId());
        //校验该水司账户下的编码，名称，商品编码唯一
        validateNameAndCodeAndProductCodeUniqueOnSameAccount(updateReqVO.getId(),updateReqVO.getName(),updateReqVO.getCode(),
                updateReqVO.getProductCode(),updateReqVO.getAccountId());
        // 更新
        InvoiceTaxrateDO updateObj = BeanUtils.toBean(updateReqVO, InvoiceTaxrateDO.class);
        invoiceTaxrateMapper.updateById(updateObj);
    }

    @Override
    public void deleteInvoiceTaxrate(Long id) {
        // 校验存在
        validateInvoiceTaxrateExists(id);
        // 删除
        invoiceTaxrateMapper.deleteById(id);
    }

    @Override
        public void deleteInvoiceTaxrateListByIds(List<Long> ids) {
        // 校验存在
        validateInvoiceTaxrateExists(ids);
        // 删除
        invoiceTaxrateMapper.deleteByIds(ids);
        }

    private void validateInvoiceTaxrateExists(List<Long> ids) {
        List<InvoiceTaxrateDO> list = invoiceTaxrateMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(INVOICE_TAXRATE_NOT_EXISTS);
        }
    }

    private void validateInvoiceTaxrateExists(Long id) {
        if (invoiceTaxrateMapper.selectById(id) == null) {
            throw exception(INVOICE_TAXRATE_NOT_EXISTS);
        }
    }

    @Override
    public InvoiceTaxrateDO getInvoiceTaxrate(Long id) {
        return invoiceTaxrateMapper.selectById(id);
    }

    @Override
    public PageResult<InvoiceTaxrateRespVO> getInvoiceTaxratePage(InvoiceTaxratePageReqVO pageReqVO) {
        PageResult<InvoiceTaxrateDO> page = invoiceTaxrateMapper.selectPage(pageReqVO);
        List<InvoiceTaxrateDO> list = page.getList();
        List<InvoiceTaxrateRespVO> respVOList = BeanUtils.toBean(list,InvoiceTaxrateRespVO.class);
        String accountName = companyAccountMapper.getNameById(pageReqVO.getAccountId());
        respVOList.stream().forEach(item -> {
            item.setAccountName(accountName);
            // 设置类型名称（使用字典转换，处理逗号分隔的值）
            if (item.getInvoiceType() != null && !item.getInvoiceType().trim().isEmpty()) {
                String[] typeValues = item.getInvoiceType().split(",");
                List<String> typeNames = new ArrayList<>();
                for (String typeValue : typeValues) {
                    String trimmedValue = typeValue.trim();
                    if (!trimmedValue.isEmpty()) {
                        String typeName = DictFrameworkUtils.parseDictDataLabel(
                                DictTypeConstants.INVOICE_WAY,
                                trimmedValue
                        );
                        // 如果字典中找不到对应的标签，则使用原值
                        typeNames.add(typeName != null ? typeName : trimmedValue);
                    }
                }
                // 将转换后的名称用逗号连接
                item.setInvoiceType(String.join(",", typeNames));
            }
        });

        return new PageResult<InvoiceTaxrateRespVO>()
                .setList(respVOList)
                .setTotal(page.getTotal());
    }

    /**
     * 校验在同一个水司账户下名称 编码 商品编码唯一
     * @param id 主键
     * @param name 名称
     * @param code 编码
     * @param productCode 商品编码
     * @param accountId 水司账户id
     */
    private void validateNameAndCodeAndProductCodeUniqueOnSameAccount(Long id,
                                                                      String name,
                                                                      String code,
                                                                      String productCode,
                                                                      Long accountId){
        validateNameUniqueOnSameAccount(id,name,accountId);
        validateCodeUniqueOnSameAccount(id,code,accountId);
        validateProductCodeUniqueOnSameAccount(id,productCode,accountId);

    }
    /**
     * 校验在同一个水司账户下名称唯一
     * @param id 主键
     * @param name 名称
     * @param accountId 水司账户id
     */
    private void validateNameUniqueOnSameAccount(Long id,String name,Long accountId){
        InvoiceTaxrateDO invoiceTaxrateDO = invoiceTaxrateMapper.selectOne(InvoiceTaxrateDO::getName,name,
                InvoiceTaxrateDO::getAccountId,accountId);
        if (invoiceTaxrateDO == null){
            return;
        }
        if (id == null || ObjUtil.notEqual(id,invoiceTaxrateDO.getId())){
            throw exception(INVOICE_TAXRATE_NAME_EXISTS);
        }
    }
    /**
     * 校验在同一个水司账户下编码唯一
     * @param id 主键
     * @param code 编码
     * @param accountId 水司账户id
     */
    private void validateCodeUniqueOnSameAccount(Long id,String code,Long accountId){
        InvoiceTaxrateDO invoiceTaxrateDO = invoiceTaxrateMapper.selectOne(InvoiceTaxrateDO::getCode,code,
                InvoiceTaxrateDO::getAccountId,accountId);
        if (invoiceTaxrateDO == null){
            return;
        }
        if (id == null || ObjUtil.notEqual(id,invoiceTaxrateDO.getId())){
            throw exception(INVOICE_TAXRATE_CODE_EXISTS);
        }
    }
    /**
     * 校验在同一个水司账户下商品编码唯一
     * @param id 主键
     * @param productCode 商品编码
     * @param accountId 水司账户id
     */
    private void validateProductCodeUniqueOnSameAccount(Long id,String productCode,Long accountId){
        InvoiceTaxrateDO invoiceTaxrateDO = invoiceTaxrateMapper.selectOne(InvoiceTaxrateDO::getProductCode,productCode,
                InvoiceTaxrateDO::getAccountId,accountId);
        if (invoiceTaxrateDO == null){
            return;
        }
        if (id == null || ObjUtil.notEqual(id,invoiceTaxrateDO.getId())){
            throw exception(INVOICE_TAXRATE_PRODUCT_CODE_EXISTS);
        }
    }

}