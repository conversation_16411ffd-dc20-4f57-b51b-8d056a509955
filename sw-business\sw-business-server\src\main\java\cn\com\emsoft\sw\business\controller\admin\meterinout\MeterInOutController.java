package cn.com.emsoft.sw.business.controller.admin.meterinout;

import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.*;
import cn.com.emsoft.sw.business.dal.dataobject.meterinout.MeterInOutDO;
import cn.com.emsoft.sw.business.service.meterinout.MeterInOutService;
import cn.com.emsoft.sw.business.util.ExcelFileNameUtils;
import cn.com.emsoft.sw.framework.common.pojo.PageParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import java.io.InputStream;


import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.pojo.CommonResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;
import static cn.com.emsoft.sw.framework.common.pojo.CommonResult.success;

import cn.com.emsoft.sw.framework.excel.core.util.ExcelUtils;

import cn.com.emsoft.sw.framework.apilog.core.annotation.ApiAccessLog;
import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.*;
import static cn.com.emsoft.sw.framework.apilog.core.enums.OperateTypeEnum.*;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;

@Tag(name = "管理后台 - 水表出/入库单")
@RestController
@RequestMapping("/business/meter-in-out")
@Validated
public class MeterInOutController {

    @Resource
    private MeterInOutService meterInOutService;

//    @PostMapping("/create")
//    @Operation(summary = "创建水表出/入库单")
//    @PreAuthorize("@ss.hasPermission('business:meter-in-out:create')")
    public CommonResult<Long> createMeterInOut(@Valid @RequestBody MeterInOutSaveReqVO createReqVO) {
        return success(meterInOutService.createMeterInOut(createReqVO));
    }

//    @PutMapping("/update")
//    @Operation(summary = "更新水表出/入库单")
//    @PreAuthorize("@ss.hasPermission('business:meter-in-out:update')")
    public CommonResult<Boolean> updateMeterInOut(@Valid @RequestBody MeterInOutSaveReqVO updateReqVO) {
        meterInOutService.updateMeterInOut(updateReqVO);
        return success(true);
    }

//    @DeleteMapping("/delete")
//    @Operation(summary = "删除水表出/入库单")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('business:meter-in-out:delete')")
    public CommonResult<Boolean> deleteMeterInOut(@RequestParam("id") Long id) {
        meterInOutService.deleteMeterInOut(id);
        return success(true);
    }

//    @DeleteMapping("/delete-list")
//    @Parameter(name = "ids", description = "编号", required = true)
//    @Operation(summary = "批量删除水表出/入库单")
//    @PreAuthorize("@ss.hasPermission('business:meter-in-out:delete')")
    public CommonResult<Boolean> deleteMeterInOutList(@RequestParam("ids") List<Long> ids) {
        meterInOutService.deleteMeterInOutListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水表出/入库单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:query')")
    public CommonResult<MeterInOutRespVO> getMeterInOut(@RequestParam("id") Long id) {
        MeterInOutDO meterInOut = meterInOutService.getMeterInOut(id);
        return success(BeanUtils.toBean(meterInOut, MeterInOutRespVO.class));
    }

    @GetMapping("/get-details")
    @Operation(summary = "获得水表出/入库单详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:query')")
    public CommonResult<List<MeterInOutDetailsRespVO>> getMeterInOutDetails(@RequestParam("id") Long id) {
        List<MeterInOutDetailsRespVO> details = meterInOutService.getMeterInOutDetails(id);
        return success(details);
    }

    @GetMapping("/page")
    @Operation(summary = "获得水表出/入库单分页")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:query')")
    public CommonResult<PageResult<MeterInOutPageRespVO>> getMeterInOutPage(@Valid MeterInOutPageReqVO pageReqVO) {
        PageResult<MeterInOutPageRespVO> pageResult = meterInOutService.getMeterInOutPageWithBusiness(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/page-details")
    @Operation(summary = "获得水表出/入库单分页明细")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:query')")
    public CommonResult<PageResult<MeterInOutPageDetailsRespVO>> getMeterInOutPageDetails(@Valid MeterInOutPageDetailsReqVO pageReqVO) {
        PageResult<MeterInOutPageDetailsRespVO> pageResult = meterInOutService.getMeterInOutPageDetails(pageReqVO);
        return success(pageResult);
    }


    @GetMapping("/export")
    @Operation(summary = "导出水表出/入库单 Excel")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMeterInOutExcel(@Valid MeterInOutPageReqVO pageReqVO,
            HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MeterInOutPageRespVO> list = meterInOutService.getMeterInOutPageWithBusiness(pageReqVO).getList();

        //获得本地模板输入流
        InputStream templateStream = getClass().getResourceAsStream("/templates/excel/meter-in-out-template.xlsx");
        if (templateStream == null){
            throw exception(METER_IN_OUT_EXPORT_MISSING_TEMPLATE);
        }

        // 生成文件名
        String filename = ExcelFileNameUtils.generateFileName("表务-出入库清单");

        // 基于模板导出 Excel
        ExcelUtils.writeWithTemplate(response, filename, templateStream,
                "数据", MeterInOutPageRespVO.class, list);
    }

    @GetMapping("/export-details")
    @Operation(summary = "导出水表出/入库单明细 Excel")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMeterInOutDetailsExcel(@Valid MeterInOutPageDetailsReqVO pageReqVO,
                                           HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MeterInOutPageDetailsRespVO> list = meterInOutService.getMeterInOutPageDetails(pageReqVO).getList();

        //获得本地模板输入流
        InputStream templateStream = getClass().getResourceAsStream("/templates/excel/meter-in-out-details-template.xlsx");
        if (templateStream == null){
            throw exception(METER_IN_OUT_EXPORT_DETAILS_MISSING_TEMPLATE);
        }

        // 生成文件名
        String filename = ExcelFileNameUtils.generateFileName("表务-出入库明细清单");

        // 基于模板导出 Excel
        ExcelUtils.writeWithTemplate(response, filename, templateStream,
                "数据", MeterInOutPageDetailsRespVO.class, list);
    }

    @PostMapping("/meter-in-check")
    @Operation(summary = "校验水表信息Excel导入数据")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "false")
    })
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:import')")
    @ApiAccessLog(operateType = IMPORT)
    public CommonResult<MeterImportRespVO> meterInCheck(@RequestParam("file") MultipartFile file,
            @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport)
            throws Exception {
        // 1. 校验文件
        if (file == null || file.isEmpty()) {
            throw exception(METER_IMPORT_EXCEL_EMPTY);
        }

        // 2. 读取Excel数据
        List<MeterImportVO> list = ExcelUtils.read(file, MeterImportVO.class);

        // 3. 执行数据校验（不插入数据库）
        MeterImportRespVO result = meterInOutService.checkMeterImportList(list);

        // 4. 返回校验结果（让前端根据checkResult.isAllValid判断是否可以导入）
        return success(result);
    }

    @PostMapping("/meter-out-check")
    @Operation(summary = "校验水表出库Excel导入数据")
    @Parameters({
            @Parameter(name = "file", description = "Excel 文件", required = true),
            @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "false")
    })
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:import')")
    @ApiAccessLog(operateType = IMPORT)
    public CommonResult<MeterImportRespVO> meterOutCheck(@RequestParam("file") MultipartFile file,
            @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport)
            throws Exception {
        // 1. 校验文件
        if (file == null || file.isEmpty()) {
            throw exception(METER_IMPORT_EXCEL_EMPTY);
        }

        // 2. 读取Excel数据
        List<MeterOutImportVO> list = ExcelUtils.read(file, MeterOutImportVO.class);

        // 3. 执行数据校验（不执行出库操作）
        MeterImportRespVO result = meterInOutService.checkMeterOutImportList(list);

        // 4. 返回校验结果（让前端根据checkResult.isAllValid判断是否可以出库）
        return success(result);

    }

    @PostMapping("/meter-in")
    @Operation(summary = "水表入库")
    @PreAuthorize("@ss.hasPermission('business:meter-in:create')")
    @ApiAccessLog(operateType = CREATE)
    public CommonResult<Long> meterIn(@Valid @RequestBody MeterInCreateVO createVO) {
        Long inOutId = meterInOutService.meterIn(createVO);
        return success(inOutId);
    }

    @PostMapping("/meter-out")
    @Operation(summary = "水表出库")
    @PreAuthorize("@ss.hasPermission('business:meter-out:create')")
    @ApiAccessLog(operateType = CREATE)
    public CommonResult<Long> meterOut(@Valid @RequestBody MeterOutCreateVO createVO) {
        Long outId = meterInOutService.meterOut(createVO);
        return success(outId);
    }

    @GetMapping("/get-meter-in-template")
    @Operation(summary = "获得水表入库导入模板")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:get')")
    public void getMeterInTemplate(HttpServletResponse response) throws IOException {
        // 读取模板文件
        InputStream templateStream = getClass().getResourceAsStream("/templates/excel/meter-import-template.xlsx");
        if (templateStream == null) {
            throw exception(METER_IMPORT_IN_TEMPLATE_NOT_EXIST_ERROR);
        }
        
        // 生成文件名：新建入库yyyy-MM-dd 6位随机数
        String filename = ExcelFileNameUtils.generateTemplateFileName("新建入库");
        
        // 输出模板文件
        ExcelUtils.writeTemplate(response, filename, templateStream);
    }

    @GetMapping("/get-meter-out-template")
    @Operation(summary = "获得水表出库导入模板")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out:get')")
    public void getMeterOutTemplate(HttpServletResponse response) throws IOException {
        // 读取模板文件
        InputStream templateStream = getClass().getResourceAsStream("/templates/excel/meter-out-template.xlsx");
        if (templateStream == null) {
            throw exception(METER_IMPORT_OUT_TEMPLATE_NOT_EXIST_ERROR);
        }
        
        // 生成文件名：新建出库yyyy-MM-dd 6位随机数
        String filename = ExcelFileNameUtils.generateTemplateFileName("新建出库");
        
        // 输出模板文件
        ExcelUtils.writeTemplate(response, filename, templateStream);
    }



}