package cn.com.emsoft.sw.business.dal.mysql.deptcustnorulerel;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel.vo.DeptCustNoRuleRelPageReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.deptcustnorulerel.DeptCustNoRuleRelDO;
import cn.com.emsoft.sw.business.dal.dataobject.deptcustnorulerel.DeptCustNoRuleRelDORef;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;

import org.apache.ibatis.annotations.Mapper;


/**
 * 户号规则和字典关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DeptCustNoRuleRelMapper extends BaseMapperX<DeptCustNoRuleRelDO> {

    default PageResult<DeptCustNoRuleRelDO> selectPage(DeptCustNoRuleRelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DeptCustNoRuleRelDO>()
                // 多字段模糊和排序
                .getSearchAndSort(DeptCustNoRuleRelDORef.class, reqVO.getSearch(), reqVO.getSearchContent(), reqVO.getSort())
                .eqIfPresent(DeptCustNoRuleRelDO::getDeptCode, reqVO.getDeptCode())
                .eqIfPresent(DeptCustNoRuleRelDO::getCustRuleId, reqVO.getCustRuleId())
                .eqIfPresent(DeptCustNoRuleRelDO::getRemark, reqVO.getRemark())
                .orderByDesc(DeptCustNoRuleRelDO::getId));
    }

}