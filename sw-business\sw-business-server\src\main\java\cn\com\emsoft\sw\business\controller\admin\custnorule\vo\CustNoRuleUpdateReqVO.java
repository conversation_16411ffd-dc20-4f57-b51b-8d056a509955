package cn.com.emsoft.sw.business.controller.admin.custnorule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 户号规则新增/修改 Request VO")
@Data
public class CustNoRuleUpdateReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "30839")
    @NotNull(message = "主键不能为空")
    private Long id;

    @Schema(description = "最大值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "最大值不能为空")
    private Integer seqNo;

    @Schema(description = "规则正则表达式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "规则表达式不能为空")
    private String seqRegex;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}