package cn.com.emsoft.sw.business.dal.dataobject.invoicetaxrate;

import cn.com.emsoft.sw.framework.common.validation.GetterRef;
import cn.com.emsoft.sw.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.com.emsoft.sw.framework.mybatis.core.dataobject.BaseDO;

/**
 * 发票税率 DO
 *
 * <AUTHOR>
 */
@TableName("biz_invoice_taxrate")
@KeySequence("biz_invoice_taxrate_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@GetterRef
public class InvoiceTaxrateDO extends TenantBaseDO {

    /**
     * 主键Id
     */
    @TableId
    private Long id;
    /**
     * 销售方/水司账户id
     */
    private Long accountId;
    /**
     * 编码
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 商品编码（唯一）
     */
    private String productCode;
    /**
     * 零税率标识
     */
    private String taxRateMark;
    /**
     * 规格型号
     */
    private String specificationType;
    /**
     * 税率百分比
     */
    private BigDecimal taxRate;
    /**
     * 项目单位
     */
    private String projectUnit;
    /**
     * 开票类型（多值，逗号分隔）
     */
    private String invoiceType;
    /**
     * 状态：0-否，1-是
     */
    private Short status;


}