package cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 户号规则和字典关系新增/修改 Request VO")
@Data
public class DeptCustNoRuleRelSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19564")
    private Long id;

    @Schema(description = "部门代码，关联系统部门表", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "部门代码，关联系统部门表不能为空")
    private String deptCode;

    @Schema(description = "户号规则id，关联户号规则表", requiredMode = Schema.RequiredMode.REQUIRED, example = "31376")
    @NotNull(message = "户号规则id，关联户号规则表不能为空")
    private Long custRuleId;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "状态：0-否，1-是", example = "1")
    private Short status;

}