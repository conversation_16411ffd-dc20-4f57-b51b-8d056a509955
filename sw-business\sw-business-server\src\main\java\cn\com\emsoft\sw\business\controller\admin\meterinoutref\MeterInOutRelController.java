package cn.com.emsoft.sw.business.controller.admin.meterinoutref;

import cn.com.emsoft.sw.business.controller.admin.meterinoutref.vo.MeterInOutRelPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterinoutref.vo.MeterInOutRelRespVO;
import cn.com.emsoft.sw.business.controller.admin.meterinoutref.vo.MeterInOutRelSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinoutref.MeterInOutRelDO;
import cn.com.emsoft.sw.business.service.meterinoutref.MeterInOutRelService;
import cn.com.emsoft.sw.framework.common.pojo.PageParam;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.pojo.CommonResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;
import static cn.com.emsoft.sw.framework.common.pojo.CommonResult.success;

import cn.com.emsoft.sw.framework.excel.core.util.ExcelUtils;

import cn.com.emsoft.sw.framework.apilog.core.annotation.ApiAccessLog;
import static cn.com.emsoft.sw.framework.apilog.core.enums.OperateTypeEnum.*;



@Tag(name = "管理后台 - 水表出/入库详情")
@RestController
@RequestMapping("/business/meter-in-out-ref")
@Validated
public class MeterInOutRelController {

    @Resource
    private MeterInOutRelService meterInOutRelService;

    @PostMapping("/create")
    @Operation(summary = "创建水表出/入库详情")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out-ref:create')")
    public CommonResult<Long> createMeterInOutRef(@Valid @RequestBody MeterInOutRelSaveReqVO createReqVO) {
        return success(meterInOutRelService.createMeterInOutRef(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新水表出/入库详情")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out-ref:update')")
    public CommonResult<Boolean> updateMeterInOutRef(@Valid @RequestBody MeterInOutRelSaveReqVO updateReqVO) {
        meterInOutRelService.updateMeterInOutRef(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除水表出/入库详情")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('business:meter-in-out-ref:delete')")
    public CommonResult<Boolean> deleteMeterInOutRef(@RequestParam("id") Long id) {
        meterInOutRelService.deleteMeterInOutRef(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除水表出/入库详情")
                @PreAuthorize("@ss.hasPermission('business:meter-in-out-ref:delete')")
    public CommonResult<Boolean> deleteMeterInOutRefList(@RequestParam("ids") List<Long> ids) {
        meterInOutRelService.deleteMeterInOutRefListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得水表出/入库详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out-ref:query')")
    public CommonResult<MeterInOutRelRespVO> getMeterInOutRef(@RequestParam("id") Long id) {
        MeterInOutRelDO meterInOutRef = meterInOutRelService.getMeterInOutRef(id);
        return success(BeanUtils.toBean(meterInOutRef, MeterInOutRelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得水表出/入库详情分页")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out-ref:query')")
    public CommonResult<PageResult<MeterInOutRelRespVO>> getMeterInOutRefPage(@Valid MeterInOutRelPageReqVO pageReqVO) {
        PageResult<MeterInOutRelDO> pageResult = meterInOutRelService.getMeterInOutRefPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, MeterInOutRelRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出水表出/入库详情 Excel")
    @PreAuthorize("@ss.hasPermission('business:meter-in-out-ref:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportMeterInOutRefExcel(@Valid MeterInOutRelPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<MeterInOutRelDO> list = meterInOutRelService.getMeterInOutRefPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "水表出/入库详情.xls", "数据", MeterInOutRelRespVO.class,
                        BeanUtils.toBean(list, MeterInOutRelRespVO.class));
    }

}