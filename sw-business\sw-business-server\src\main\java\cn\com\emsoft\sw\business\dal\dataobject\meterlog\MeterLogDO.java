package cn.com.emsoft.sw.business.dal.dataobject.meterlog;

import cn.com.emsoft.sw.framework.common.validation.GetterRef;
import cn.com.emsoft.sw.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.com.emsoft.sw.framework.mybatis.core.dataobject.BaseDO;

/**
 * 表务日志 DO
 *
 * <AUTHOR>
 */
@TableName("biz_meter_log")
@KeySequence("biz_meter_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@GetterRef
public class MeterLogDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 水表id，关联水表信息
     */
    private Long meterId;
    /**
     * 水表出入库id，为出入库时有值
     */
    private Long meterInOutId;
    /**
     * 日志类型，关联日志类型
     */
    private Short type;
    /**
     * 操作结果，成功/失败
     */
    private String result;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态：0-否，1-是
     */
    private Short status;


}