package cn.com.emsoft.sw.business.dal.mysql.companyaccount;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.companyaccount.vo.CompanyAccountPageReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.companyaccount.CompanyAccountDO;
import cn.com.emsoft.sw.business.dal.dataobject.companyaccount.CompanyAccountDORef;
import cn.com.emsoft.sw.framework.common.enums.CommonStatusEnum;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;

/**
 * 水司账户 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CompanyAccountMapper extends BaseMapperX<CompanyAccountDO> {

    default PageResult<CompanyAccountDO> selectPage(CompanyAccountPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CompanyAccountDO>()
                .getSearchAndSort(CompanyAccountDORef.class,reqVO.getSearch(), reqVO.getSearchContent(), reqVO.getSort())
                .likeIfPresent(CompanyAccountDO::getAccountName, reqVO.getAccountName())
                .likeIfPresent(CompanyAccountDO::getTaxNumber, reqVO.getTaxNumber())
                .likeIfPresent(CompanyAccountDO::getAccountAddress, reqVO.getAccountAddress())
                .likeIfPresent(CompanyAccountDO::getPhone, reqVO.getPhone())
                .eqIfPresent(CompanyAccountDO::getBankName, reqVO.getBankName())
                .likeIfPresent(CompanyAccountDO::getBankAccount, reqVO.getBankAccount())
                .orderByDesc(CompanyAccountDO::getId));
    }

    default List<CompanyAccountDO> selectSimpleCompanyAccountList(){
        return selectList(new LambdaQueryWrapperX<CompanyAccountDO>()
                .eq(CompanyAccountDO::getStatus, CommonStatusEnum.ENABLE.getStatus())
                .orderByAsc(CompanyAccountDO::getAccountName));
    }

    default CompanyAccountDO selectByTaxNumber(String taxNumber) {
        return selectOne(CompanyAccountDO::getTaxNumber, taxNumber);
    }

    default CompanyAccountDO selectByAccountName(String name){
        return selectOne(CompanyAccountDO::getAccountName,name);
    }

    default String getNameById(Long id){
        CompanyAccountDO companyAccountDO = selectOne(CompanyAccountDO::getId, id);
        if (companyAccountDO == null){
            return null;
        }
        return companyAccountDO.getAccountName();
    }

}