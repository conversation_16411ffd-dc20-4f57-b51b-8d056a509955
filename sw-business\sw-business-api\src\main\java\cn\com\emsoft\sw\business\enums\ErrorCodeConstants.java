package cn.com.emsoft.sw.business.enums;

import cn.com.emsoft.sw.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {
    // ========== 水表厂家 1-025-000-000==========
    ErrorCode METER_MAKER_NOT_EXISTS = new ErrorCode(1_025_000_000, "水表厂家不存在");
    ErrorCode METER_MAKER_CODE_EXISTS = new ErrorCode(1_025_000_001, "水表厂家代码已存在");
    ErrorCode METER_MAKER_CREATE_FAIL = new ErrorCode(1_025_000_002, "创建失败，请联系管理员处理");
    ErrorCode METER_MAKER_HAS_RELATED_MODELS = new ErrorCode(1_025_000_003, "删除失败，该水表厂家下还存在水表型号数据");
    ErrorCode METER_MAKER_NAME_EXISTS = new ErrorCode(1_025_000_004, "水表厂家名称已存在");
    ErrorCode METER_MAKER_NOT_ENABLE = new ErrorCode(1_025_000_005, "水表厂家未启用");

    // ========== 水表型号 1-025-001-000 ==========
    ErrorCode METER_MODEL_NOT_EXISTS = new ErrorCode(1_025_001_000, "水表型号不存在");

    ErrorCode METER_MODEL_CODE_EXISTS = new ErrorCode(1_025_001_001, "水表口型号代码已存在");
    ErrorCode METER_MODEL_CREATE_FAIL = new ErrorCode(1_025_001_002, "创建失败，请联系管理员处理");
    ErrorCode METER_MODEL_NAME_EXISTS = new ErrorCode(1_025_001_003, "水表口型号名称已存在");
    ErrorCode METER_MODEL_NOT_ENABLE = new ErrorCode(1_025_001_004, "水表厂家未启用");

    // ========== 水表口径 1-025-002-000 ==========
    ErrorCode METER_CALIBER_NOT_EXISTS = new ErrorCode(1_025_002_000, "水表口径不存在");

    ErrorCode METER_CALIBER_NAME_EXISTS = new ErrorCode(1_025_002_001, "水表口径名称已存在");

    ErrorCode METER_CALIBER_CREATE_FAIL = new ErrorCode(1_025_002_002, "创建失败，请联系管理员处理");

    ErrorCode METER_CALIBER_COEFFICIENT_WRONG = new ErrorCode(1_025_002_003, "水表口径量高系数需要比量低系数大");

    ErrorCode METER_CALIBER_VALUE_EXISTS = new ErrorCode(1_025_002_004, "水表口径值已存在");
    ErrorCode METER_CALIBER_NOT_ENABLE = new ErrorCode(1_025_002_005, "水表口径未启用");

    // ========== 水表量程 1-025-003-000 ==========
    ErrorCode METER_RANGE_NOT_EXISTS = new ErrorCode(1_025_003_000, "水表量程不存在");
    ErrorCode METER_RANGE_CODE_EXISTS = new ErrorCode(1_025_003_001, "水表量程代码已存在");
    ErrorCode METER_RANGE_CREATE_FAIL = new ErrorCode(1_025_003_002, "创建失败，请联系管理员处理");
    ErrorCode METER_RANGE_NAME_EXISTS = new ErrorCode(1_025_003_003, "水表量程名称已存在");
    ErrorCode METER_RANGE_VALUE_EXISTS = new ErrorCode(1_025_003_004, "水表量程值已存在");
    ErrorCode METER_RANGE_NOT_ENABLE = new ErrorCode(1_025_003_005, "水表量程未启用");

    // ========== 所属小区 1-025-004-000 ==========
    ErrorCode COMMUNITY_NOT_EXISTS = new ErrorCode(1_025_004_000, "所属小区不存在");
    ErrorCode COMMUNITY_CODE_EXISTS = new ErrorCode(1_025_004_001, "小区代码已经存在");
    ErrorCode COMMUNITY_CREATE_FAIL = new ErrorCode(1_025_004_002, "创建失败，请联系管理员处理");

    ErrorCode COMMUNITY_SELF_PARENT_ERROR = new ErrorCode(1_025_004_003, "不能设置自己为父级小区");
    ErrorCode COMMUNITY_SON_PARENT_ERROR = new ErrorCode(1_025_004_004, "不能设置子孙小区为父级小区");

    ErrorCode COMMUNITY_HAS_RELATED_SON = new ErrorCode(1_025_004_005, "删除失败，该小区还存在关联的子小区");
    ErrorCode DEPT_CODE_UPDATE_COMMUNITY_FAIL = new ErrorCode(1_025_004_006, "更新小区部门代码失败");

    // ========== 水司账户 1_025_005_000 ==========
    ErrorCode COMPANY_ACCOUNT_NOT_EXISTS = new ErrorCode(1_025_005_000, "水司账户不存在");
    ErrorCode COMPANY_ACCOUNT_TAX_NUMBER_EXISTS = new ErrorCode(1_025_005_001, "纳税人识别号已存在");
    ErrorCode COMPANY_ACCOUNT_NAME_EXISTS = new ErrorCode(1_025_005_002, "销售方名称已存在");
    // ========== 水价归属 1_025_006_000 ==========
    ErrorCode PRICE_CATEGORY_NOT_EXISTS = new ErrorCode(1_025_006_000, "水价归属不存在");
    ErrorCode PRICE_CATEGORY_NAME_EXISTS = new ErrorCode(1_025_006_001, "水价归属名称已经存在");
    ErrorCode PRICE_CATEGORY_CODE_EXISTS = new ErrorCode(1_025_006_002, "水价归属代码已经存在");

    ErrorCode PRICE_CATEGORY_SELF_PARENT_ERROR = new ErrorCode(1_025_006_003, "不能设置自己为父级水价归属");
    ErrorCode PRICE_CATEGORY_SON_BE_PARENT_ERROR = new ErrorCode(1_025_006_004, "不能设置子孙水价归属为父级水价归属");

    ErrorCode PRICE_CATEGORY_HAS_RELATED_SON = new ErrorCode(1_025_006_005, "删除失败，该水价归属还存在关联的子水价归属");
    ErrorCode PRICE_CATEGORY_CREATE_FAIL = new ErrorCode(1_025_006_006, "创建失败，请联系管理员处理");

    // ========== 费用组成 1_025_007_000 ==========
    ErrorCode COST_COMPONENT_NOT_EXISTS = new ErrorCode(1_025_007_000, "费用组成不存在");
    ErrorCode COST_COMPONENT_CODE_EXISTS = new ErrorCode(1_025_007_001, "费用组成代码已存在");
    ErrorCode COST_COMPONENT_NAME_EXISTS = new ErrorCode(1_025_007_002, "费用组成名称已存在");
    ErrorCode COST_COMPONENT_CREATE_FAIL = new ErrorCode(1_025_007_003, "创建失败，请联系管理员处理");

    // ========== 计划用水方案 1_025_008_000 ==========
    ErrorCode WATER_USE_SCHEME_NOT_EXISTS = new ErrorCode(1_025_008_000, "计划用水方案不存在");
    ErrorCode WATER_USE_SCHEME_NAME_EXISTS = new ErrorCode(1_025_008_001, "计划用水方案名称已存在");
    ErrorCode WATER_USE_SCHEME_CREATE_FAIL = new ErrorCode(1_025_008_002, "计划用水方案创建失败，请联系管理员处理");

    // ========== 计划用水方案阶梯 1_025_009_000 ==========
    ErrorCode WATER_USE_SCHEME_TIER_NOT_EXISTS = new ErrorCode(1_025_009_000, "计划用水方案阶梯不存在");

    ErrorCode WATER_USE_SCHEME_TIER_CREATE_FAIL = new ErrorCode(1_025_009_001, "计划用水方案阶梯创建失败，请联系管理员处理");
    ErrorCode WATER_USE_SCHEME_TIER_UPDATE_FAIL = new ErrorCode(1_025_009_002, "计划用水方案阶梯更新失败，请联系管理员处理");
    ErrorCode WATER_USE_SCHEME_TIER_DELETE_FAIL = new ErrorCode(1_025_009_003, "计划用水方案阶梯删除失败，请联系管理员处理");

    // ========== 水价调整模版 1_025_010_000 ==========
    ErrorCode PRICE_TEMPLATE_NOT_EXISTS = new ErrorCode(1_025_010_000, "水价调整模版不存在");
    ErrorCode PRICE_TEMPLATE_NAME_EXISTS = new ErrorCode(1_025_010_001, "水价调整模版用水性质已存在");
    ErrorCode PRICE_TEMPLATE_CODE_EXISTS = new ErrorCode(1_025_010_002, "水价调整模版简号已存在");
    ErrorCode PRICE_TEMPLATE_ID_NOT_EXISTS_IN_PREVIOUS_VERSION = new ErrorCode(1_025_010_003, "模板ID在上一版本中不存在");
    ErrorCode DEPT_CODE_UPDATE_TEMPLATE_DEPT_REL_FAIL = new ErrorCode(1_025_010_004, "更新模板部门关系代码失败");
    ErrorCode PRICE_TEMPLATE_UPDATE_EMPTY_ERROR = new ErrorCode(1_025_010_005,"水价更新信息不能为空");
    ErrorCode PRICE_TEMPLATE_UPDATE_WRONG_TIER_VALUE_ERROR = new ErrorCode(1_025_010_006,"只有最后一个级别的结束水量才能等于-1！存在错误数据");
    ErrorCode PRICE_TEMPLATE_UPDATE_WRONG_WATER_VALUE_ERROR = new ErrorCode(1_025_010_007,"只有最后一个级别的结束水量才能等于-1！存在错误数据");
    ErrorCode PRICE_TEMPLATE_ADJUSTMENT_LOCK_OCCUPIED = new ErrorCode(1_025_010_008, "当前{}用户正在调价，请稍后再试");
    ErrorCode PRICE_TEMPLATE_ADJUSTMENT_LOCK_NOT_OWNED_BY_USER = new ErrorCode(1_025_010_009, "无权取消调价，当前{}用户正在调价");
    ErrorCode PRICE_TEMPLATE_ADJUSTMENT_LOCK_ACQUIRE_FAILED = new ErrorCode(1_025_010_010, "获取调价锁失败，请稍后再试");
    ErrorCode PRICE_TEMPLATE_ADJUSTMENT_LOCK_RELEASE_FAILED = new ErrorCode(1_025_010_011, "释放调价锁失败");
    ErrorCode PRICE_TEMPLATE_ADJUSTMENT_LOCK_EXPIRED = new ErrorCode(1_025_010_012, "调价锁已过期,调价时间为30分钟，请重新开始调价");

    // ========== 水价调整快照（历史） 1_025_011_000 ==========
    ErrorCode PRICE_ADJUSTMENT_SNAP_NOT_EXISTS = new ErrorCode(1_025_011_000, "水价调整快照（历史）不存在");

    // ========== 水价费用调整 1_025_012_000 ==========
    ErrorCode PRICE_COST_ADJUSTMENT_NOT_EXISTS = new ErrorCode(1_025_012_000, "水价费用调整不存在");

    // ========== 水价阶梯调整 1_025_013_000 ==========
    ErrorCode PRICE_TIER_ADJUSTMENT_NOT_EXISTS = new ErrorCode(1_025_013_000, "水价阶梯调整不存在");

    // ========== 水价调整模板和部门关系 1_025_014_000 ==========
    ErrorCode TEMPLATE_DEPT_REL_NOT_EXISTS = new ErrorCode(1_025_014_000, "水价调整模板和部门关系不存在");
    ErrorCode DEPT_HAS_RELATED_PRICE_TEMPLATES = new ErrorCode(1_025_014_001, "删除失败，该部门还存在关联的水价调整模板");

    // ========== 水价优惠费用 1_025_015_000 ==========
    ErrorCode PRICE_DISCOUNT_COST_NOT_EXISTS = new ErrorCode(1_025_015_000, "水价优惠费用不存在");

    // ========== 水价优惠阶梯 1_025_016_000 ==========
    ErrorCode PRICE_DISCOUNT_TIER_NOT_EXISTS = new ErrorCode(1_025_016_000, "水价优惠阶梯不存在");

    // ========== 水价优惠方案 1_025_017_000 ==========
    ErrorCode PRICE_DISCOUNT_SCHEME_NOT_EXISTS = new ErrorCode(1_025_017_000, "水价优惠方案不存在");
    ErrorCode PRICE_DISCOUNT_SCHEME_NAME_EXISTS = new ErrorCode(1_025_017_001, "水价优惠方案名称已存在");
    ErrorCode PRICE_DISCOUNT_SCHEME_TIER_DATA_EMPTY = new ErrorCode(1_025_017_002, "阶梯数据不能为空");
    ErrorCode PRICE_DISCOUNT_SCHEME_TIER_DATA_DISCONTINUOUS = new ErrorCode(1_025_017_003,
            "阶梯数据不连续，上一个阶梯的结束水量应等于下一个阶梯的开始水量");
    ErrorCode PRICE_DISCOUNT_SCHEME_UPDATE_FAIL = new ErrorCode(1_025_017_004, "水价优惠方案更新失败");

    // ========== 部门和水司账户关系 1_025_018_000 ==========
    ErrorCode DEPT_ACCOUNT_REL_NOT_EXISTS = new ErrorCode(1_025_018_000, "部门和水司账户关系不存在");
    ErrorCode DEPT_ACCOUNT_REL_ACCOUNT_NOT_EXISTS = new ErrorCode(1_025_018_001, "水司账户不存在");
    ErrorCode DEPT_ACCOUNT_REL_DEPT_CODE_EMPTY = new ErrorCode(1_025_018_002, "部门代码列表不能为空");
    ErrorCode DEPT_ACCOUNT_REL_CHANGE_FAIL = new ErrorCode(1_025_018_003, "部门账户关系变更失败");

    // ========== 水表出/入库单 1_025_019_000 ==========
    ErrorCode METER_IN_OUT_NOT_EXISTS = new ErrorCode(1_025_019_000, "水表出/入库单不存在");
    ErrorCode METER_IN_OUT_STOCK_TYPE_ERROR = new ErrorCode(1_025_019_001, "水表出/入库单类型有误");

    // ========== 水表信息 1_025_020_000 ==========
    ErrorCode METER_NOT_EXISTS = new ErrorCode(1_025_020_000, "水表信息不存在");
    ErrorCode METER_NOT_FOUND_BY_IDENTIFIER = new ErrorCode(1_025_020_001, "根据标识符未找到水表信息");

    // ========== 表务日志 1_025_021_000 ==========
    ErrorCode METER_LOG_NOT_EXISTS = new ErrorCode(1_025_021_000, "表务日志不存在");
    ErrorCode METER_LOG_PARAMS_MISSING = new ErrorCode(1_025_021_001, "请提供钢印号或条形码");

    // ========== 水表信息导入 1_025_022_000 ==========
    ErrorCode METER_IMPORT_EXCEL_EMPTY = new ErrorCode(1_025_022_001, "导入Excel文件为空");
    ErrorCode METER_IMPORT_DATA_EMPTY = new ErrorCode(1_025_022_002, "导入数据为空");
    ErrorCode METER_IMPORT_STEEL_MARK_DUPLICATE = new ErrorCode(1_025_022_003, "Excel第{}行钢印号重复");
    ErrorCode METER_IMPORT_SEAL_NUMBER_DUPLICATE = new ErrorCode(1_025_022_004, "Excel第{}行水表编号重复");
    ErrorCode METER_IMPORT_BAR_CODE_DUPLICATE = new ErrorCode(1_025_022_005, "Excel第{}行条形码重复");
    ErrorCode METER_IMPORT_CHECK_CODE_DUPLICATE = new ErrorCode(1_025_022_006, "Excel第{}行强检编号重复");
    ErrorCode METER_IMPORT_STEEL_MARK_EXISTS = new ErrorCode(1_025_022_007, "钢印号在数据库中已存在");
    ErrorCode METER_IMPORT_SEAL_NUMBER_EXISTS = new ErrorCode(1_025_022_008, "水表编号在数据库中已存在");
    ErrorCode METER_IMPORT_MAKER_NOT_EXISTS = new ErrorCode(1_025_022_009, "水表厂家不存在");
    ErrorCode METER_IMPORT_MODEL_NOT_EXISTS = new ErrorCode(1_025_022_010, "水表型号不存在");
    ErrorCode METER_IMPORT_CALIBER_NOT_EXISTS = new ErrorCode(1_025_022_011, "水表口径不存在");
    ErrorCode METER_IMPORT_RANGE_NOT_EXISTS = new ErrorCode(1_025_022_012, "水表量程不存在");
    ErrorCode METER_IMPORT_HAS_ERROR = new ErrorCode(1_025_022_013, "导入数据存在校验错误");
    ErrorCode METER_IMPORT_IN_TEMPLATE_NOT_EXIST_ERROR = new ErrorCode(1_025_022_014, "水表入库模板文件不存在");
    ErrorCode METER_IMPORT_OUT_TEMPLATE_NOT_EXIST_ERROR = new ErrorCode(1_025_022_015, "水表出库模板文件不存在");

    // ========== 水表入库 1_025_023_000 ==========
    ErrorCode METER_IN_DEPT_NOT_EXISTS = new ErrorCode(1_025_023_001, "营业站点不存在");
    ErrorCode METER_IN_METER_LIST_EMPTY = new ErrorCode(1_025_023_002, "水表明细列表不能为空");
    ErrorCode METER_IN_MAKER_CODE_NOT_EXISTS = new ErrorCode(1_025_023_003, "水表厂家代码不存在或未启用");
    ErrorCode METER_IN_MODEL_CODE_NOT_EXISTS = new ErrorCode(1_025_023_004, "水表型号代码不存在或未启用");
    ErrorCode METER_IN_CALIBER_CODE_NOT_EXISTS = new ErrorCode(1_025_023_005, "水表口径代码不存在或未启用");
    ErrorCode METER_IN_RANGE_CODE_NOT_EXISTS = new ErrorCode(1_025_023_006, "水表量程代码不存在或未启用");
    ErrorCode METER_IN_TYPE_NOT_EXISTS = new ErrorCode(1_025_023_007, "水表类型不存在");
    ErrorCode METER_IN_STEEL_MARK_EXISTS = new ErrorCode(1_025_023_008, "钢印号已存在");
    ErrorCode METER_IN_BAR_CODE_EXISTS = new ErrorCode(1_025_023_009, "条形码已存在");
    ErrorCode METER_IN_BATCH_STEEL_MARK_NOT_EMPTY = new ErrorCode(1_025_023_010, "批量入库时钢印号必须为空");
    ErrorCode METER_IN_BATCH_BAR_CODE_NOT_EMPTY = new ErrorCode(1_025_023_011, "批量入库时条形码必须为空");
    ErrorCode METER_IN_QUANTITY_INVALID = new ErrorCode(1_025_023_012, "数量不能为负数");
    ErrorCode METER_IN_SINGLE_QUANTITY_NOT_ZERO = new ErrorCode(1_025_023_013, "单一入库时数量必须为0或null");
    ErrorCode METER_IN_REQUEST_TOO_FREQUENTLY = new ErrorCode(1_025_023_014, "当前用户入库操作过于频繁，请稍后再试");

    // ========== 水表出/入库详情 1_025_024_000 ==========
    ErrorCode METER_IN_OUT_REF_NOT_EXISTS = new ErrorCode(1_025_024_000, "水表出/入库详情不存在");

    // ======================= METER_OUT 水表出库 1_025_025_000 =======================
    
    ErrorCode METER_OUT_METER_IDS_EMPTY = new ErrorCode(1_025_025_000, "出库水表列表不能为空");
    ErrorCode METER_OUT_METER_NOT_EXISTS = new ErrorCode(1_025_025_001, "水表不存在，ID：{}");
    ErrorCode METER_OUT_METER_STEEL_MARK_EMPTY = new ErrorCode(1_025_025_002, "水表钢印号不能为空，ID：{}");
    ErrorCode METER_OUT_METER_SEAL_NUMBER_EMPTY = new ErrorCode(1_025_025_003, "水表编号不能为空，ID：{}");
    ErrorCode METER_OUT_METER_STATUS_INVALID = new ErrorCode(1_025_025_004, "水表状态不正确，必须为入库状态，水表ID：{}，当前状态：{}");
    ErrorCode METER_OUT_ADJUST_DEPT_REQUIRED = new ErrorCode(1_025_025_005, "调库时目标仓库不能为空");
    ErrorCode METER_OUT_ADJUST_DEPT_DUPLICATE_STEEL_MARK = new ErrorCode(1_025_025_006, "目标仓库存在相同钢印号水表，不允许调库：{}");
    ErrorCode METER_OUT_REQUEST_TOO_FREQUENTLY = new ErrorCode(1_025_025_007, "当前用户出库操作过于频繁，请稍后再试");

    // ======================= 水表信息导出 1_025_026_000 =======================
    ErrorCode METER_IN_OUT_EXPORT_MISSING_TEMPLATE = new ErrorCode(1_025_026_000, "出入库导出模板不存在，请联系管理员");
    ErrorCode METER_IN_OUT_EXPORT_DETAILS_MISSING_TEMPLATE = new ErrorCode(1_025_026_001, "出入库明细导出模板不存在，请联系管理员");
    ErrorCode METER_INFOS_EXPORT_MISSING_TEMPLATE = new ErrorCode(1_025_026_002, "库存明细导出模板不存在，请联系管理员");

    // ========== 户号规则 1_025_026_000 ==========
    ErrorCode CUST_NO_RULE_NOT_EXISTS = new ErrorCode(1_025_026_000, "户号规则不存在");
    ErrorCode CUST_NO_RULE_CODE_EXISTS = new ErrorCode(1_025_026_001, "户号规则编号已存在");
    // ========== 户号规则和字典关系 1_025_027_000 ==========
    ErrorCode DEPT_CUST_NO_RULE_REL_NOT_EXISTS = new ErrorCode(1_025_027_000, "户号规则和字典关系不存在");

    // ========== 发票税率 1_025_028_000 ==========
    ErrorCode INVOICE_TAXRATE_NOT_EXISTS = new ErrorCode(1_025_028_000, "发票税率不存在");
    ErrorCode INVOICE_TAXRATE_NAME_EXISTS = new ErrorCode(1_025_028_001, "当前水司账户下，项目名称重复");
    ErrorCode INVOICE_TAXRATE_CODE_EXISTS = new ErrorCode(1_025_028_002, "当前水司账户下，项目编码重复");
    ErrorCode INVOICE_TAXRATE_PRODUCT_CODE_EXISTS = new ErrorCode(1_025_028_003, "当前水司账户下，商品编码重复");

}
