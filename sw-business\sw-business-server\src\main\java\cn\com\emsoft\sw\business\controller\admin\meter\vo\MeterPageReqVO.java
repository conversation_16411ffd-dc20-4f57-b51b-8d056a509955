package cn.com.emsoft.sw.business.controller.admin.meter.vo;

import cn.com.emsoft.sw.framework.common.pojo.SearchAndSortPageParam;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "管理后台 - 水表信息分页 Request VO")
@Data
public class MeterPageReqVO extends SearchAndSortPageParam {

    @Schema(description = "所属仓库id")
    private Long deptId;

    @Schema(description = "钢印号")
    private String steelMark;

    @Schema(description = "水表编号")
    private String sealNumber;

    @Schema(description = "强检编号")
    private String checkCode;

    @Schema(description = "水表状态")
    private Short meterStatus;

    @Schema(description = "水表厂家代码，关联水表厂家表")
    private String makerCode;

    @Schema(description = "水表型号代码，关联水表型号表")
    private String modelCode;

    @Schema(description = "水表口径代码，关联水表口径表")
    private String caliberCode;

    @Schema(description = "水表量程代码，关联水表量程表")
    private String rangeCode;

    @Schema(description = "水表分类/类型", example = "1")
    private Short type;

    @Schema(description = "客户编号", example = "1")
    private String custCode;

    @Schema(description = "工程编号", example = "1")
    private String projectCode;

}