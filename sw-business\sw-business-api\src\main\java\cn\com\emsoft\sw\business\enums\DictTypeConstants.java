package cn.com.emsoft.sw.business.enums;


/**
 * 字典类型的枚举类
 *
 * <AUTHOR>
 */
public interface DictTypeConstants {

    String METER_OTH_TYPE = "meter_oth_type"; //水表分类/类型

    String METER_STATE = "meter_state"; //水表状态

    String COMMON_STATUS ="common_status"; //系统状态

    String METER_LOG_TYPE = "meter_log_type";//水表日志类型

    String STOCK_TYPE = "stock_type"; //库存单类型

    String MW_STATE = "mw_state"; //表务状态

    String INVOICE_WAY = "invoice_way"; //发票类型

    String TAX_RATE_MARK = "tax_rate_mark"; //零税率标识
}
