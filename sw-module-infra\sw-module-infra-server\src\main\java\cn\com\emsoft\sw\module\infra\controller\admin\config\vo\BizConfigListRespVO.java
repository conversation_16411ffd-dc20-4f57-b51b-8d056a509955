package cn.com.emsoft.sw.module.infra.controller.admin.config.vo;

import cn.com.emsoft.sw.framework.excel.core.annotations.DictFormat;
import cn.com.emsoft.sw.module.infra.enums.DictTypeConstants;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Schema(description = "管理后台 - 抄表配置+参数配置信息 Response VO")
@Data
public class BizConfigListRespVO {

    @Schema(description = "参数配置序号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "参数分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "biz")
    private String category;

    @Schema(description = "参数名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "数据库名")
    private String name;

    @Schema(description = "参数键名", requiredMode = Schema.RequiredMode.REQUIRED, example = "yunai.db.username")
    private String configKey;

    @Schema(description = "参数键值", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private String value;

    @Schema(description = "参数类型，参见 SysConfigTypeEnum 枚举", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @DictFormat(DictTypeConstants.CONFIG_TYPE)
    private Integer type;

    @Schema(description = "是否可见", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @DictFormat(DictTypeConstants.BOOLEAN_STRING)
    private Boolean visible;

    @Schema(description = "是否标红", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @DictFormat(DictTypeConstants.BOOLEAN_STRING)
    private Boolean red;

    @Schema(description = "是否编辑", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    @DictFormat(DictTypeConstants.BOOLEAN_STRING)
    private Boolean edit;

    @Schema(description = "备注", example = "备注一下很帅气！")
    private String remark;

}
