package cn.com.emsoft.sw.business.controller.admin.meterinoutref.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 水表出/入库详情 Response VO")
@Data
@ExcelIgnoreUnannotated
public class MeterInOutRelRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "14471")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "水表出/入库单id，关联水表出入库单", requiredMode = Schema.RequiredMode.REQUIRED, example = "7650")
    @ExcelProperty("水表出/入库单id，关联水表出入库单")
    private Long meterInOutId;

    @Schema(description = "水表id，关联水表信息表", requiredMode = Schema.RequiredMode.REQUIRED, example = "9067")
    @ExcelProperty("水表id，关联水表信息表")
    private Long meterId;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "状态：0-否，1-是", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态：0-否，1-是")
    private Short status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人id")
    @ExcelProperty("创建人id")
    private String creator;

    @Schema(description = "更新人id")
    @ExcelProperty("更新人id")
    private String updater;

    @Schema(description = "是否删除", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否删除")
    private Short deleted;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "14729")
    @ExcelProperty("租户id")
    private Long tenantId;

}