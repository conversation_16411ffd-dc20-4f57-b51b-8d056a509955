package cn.com.emsoft.sw.business.controller.admin.meterinoutref.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 水表出/入库详情新增/修改 Request VO")
@Data
public class MeterInOutRelSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "14471")
    private Long id;

    @Schema(description = "水表出/入库单id，关联水表出入库单", requiredMode = Schema.RequiredMode.REQUIRED, example = "7650")
    @NotNull(message = "水表出/入库单id，关联水表出入库单不能为空")
    private Long meterInOutId;

    @Schema(description = "水表id，关联水表信息表", requiredMode = Schema.RequiredMode.REQUIRED, example = "9067")
    @NotNull(message = "水表id，关联水表信息表不能为空")
    private Long meterId;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "状态：0-否，1-是", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态：0-否，1-是不能为空")
    private Short status;

}