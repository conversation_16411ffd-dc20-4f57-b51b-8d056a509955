package cn.com.emsoft.sw.business.service.meterinoutref;

import cn.com.emsoft.sw.business.controller.admin.meterinoutref.vo.MeterInOutRelPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterinoutref.vo.MeterInOutRelSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinoutref.MeterInOutRelDO;
import cn.com.emsoft.sw.business.dal.mysql.meterinoutref.MeterInOutRelMapper;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;

import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.METER_IN_OUT_REF_NOT_EXISTS;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;



/**
 * 水表出/入库详情 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MeterInOutRelServiceImpl implements MeterInOutRelService {

    @Resource
    private MeterInOutRelMapper meterInOutRelMapper;

    @Override
    public Long createMeterInOutRef(MeterInOutRelSaveReqVO createReqVO) {
        // 插入
        MeterInOutRelDO meterInOutRef = BeanUtils.toBean(createReqVO, MeterInOutRelDO.class);
        meterInOutRelMapper.insert(meterInOutRef);
        // 返回
        return meterInOutRef.getId();
    }

    @Override
    public void updateMeterInOutRef(MeterInOutRelSaveReqVO updateReqVO) {
        // 校验存在
        validateMeterInOutRefExists(updateReqVO.getId());
        // 更新
        MeterInOutRelDO updateObj = BeanUtils.toBean(updateReqVO, MeterInOutRelDO.class);
        meterInOutRelMapper.updateById(updateObj);
    }

    @Override
    public void deleteMeterInOutRef(Long id) {
        // 校验存在
        validateMeterInOutRefExists(id);
        // 删除
        meterInOutRelMapper.deleteById(id);
    }

    @Override
        public void deleteMeterInOutRefListByIds(List<Long> ids) {
        // 校验存在
        validateMeterInOutRefExists(ids);
        // 删除
        meterInOutRelMapper.deleteByIds(ids);
        }

    private void validateMeterInOutRefExists(List<Long> ids) {
        List<MeterInOutRelDO> list = meterInOutRelMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(METER_IN_OUT_REF_NOT_EXISTS);
        }
    }

    private void validateMeterInOutRefExists(Long id) {
        if (meterInOutRelMapper.selectById(id) == null) {
            throw exception(METER_IN_OUT_REF_NOT_EXISTS);
        }
    }

    @Override
    public MeterInOutRelDO getMeterInOutRef(Long id) {
        return meterInOutRelMapper.selectById(id);
    }

    @Override
    public PageResult<MeterInOutRelDO> getMeterInOutRefPage(MeterInOutRelPageReqVO pageReqVO) {
        return meterInOutRelMapper.selectPage(pageReqVO);
    }

}