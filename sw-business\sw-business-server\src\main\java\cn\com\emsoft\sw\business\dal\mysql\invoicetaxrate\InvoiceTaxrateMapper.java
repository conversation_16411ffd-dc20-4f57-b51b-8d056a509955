package cn.com.emsoft.sw.business.dal.mysql.invoicetaxrate;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxratePageReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.invoicetaxrate.InvoiceTaxrateDO;
import cn.com.emsoft.sw.business.dal.dataobject.invoicetaxrate.InvoiceTaxrateDORef;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;

/**
 * 发票税率 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InvoiceTaxrateMapper extends BaseMapperX<InvoiceTaxrateDO> {

    default PageResult<InvoiceTaxrateDO> selectPage(InvoiceTaxratePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InvoiceTaxrateDO>()
                // 多字段模糊和排序
                .getSearchAndSort(InvoiceTaxrateDORef.class, reqVO.getSearch(), reqVO.getSearchContent(), reqVO.getSort())
                .eqIfPresent(InvoiceTaxrateDO::getAccountId, reqVO.getAccountId())
                .orderByDesc(InvoiceTaxrateDO::getId));
    }

}