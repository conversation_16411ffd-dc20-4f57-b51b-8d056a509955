package cn.com.emsoft.sw.business.service.custnorule;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.custnorule.vo.CustNoRuleCreateReqVO;
import cn.com.emsoft.sw.business.controller.admin.custnorule.vo.CustNoRulePageReqVO;
import cn.com.emsoft.sw.business.controller.admin.custnorule.vo.CustNoRuleUpdateReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.custnorule.CustNoRuleDO;
import jakarta.validation.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;


/**
 * 户号规则 Service 接口
 *
 * <AUTHOR>
 */
public interface CustNoRuleService {

    /**
     * 创建户号规则
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createCustNoRule(@Valid CustNoRuleCreateReqVO createReqVO);

    /**
     * 更新户号规则
     *
     * @param updateReqVO 更新信息
     */
    void updateCustNoRule(@Valid CustNoRuleUpdateReqVO updateReqVO);

    /**
     * 删除户号规则
     *
     * @param id 编号
     */
    void deleteCustNoRule(Long id);

    /**
    * 批量删除户号规则
    *
    * @param ids 编号
    */
    void deleteCustNoRuleListByIds(List<Long> ids);

    /**
     * 获得户号规则
     *
     * @param id 编号
     * @return 户号规则
     */
    CustNoRuleDO getCustNoRule(Long id);

    /**
     * 获得户号规则分页
     *
     * @param pageReqVO 分页查询
     * @return 户号规则分页
     */
    PageResult<CustNoRuleDO> getCustNoRulePage(CustNoRulePageReqVO pageReqVO);

}