package cn.com.emsoft.sw.business.enums;

/**
 * Business 错误提示信息常量
 * 目的：统一管理错误提示信息，便于维护和国际化
 *
 * <AUTHOR>
 */
public interface ErrorMessageConstants {

    // ======================= METER_IMPORT 水表导入 =======================

    String METER_IMPORT_STEEL_MARK_REQUIRED = "钢印号不能为空";
    String METER_IMPORT_MAKER_NAME_REQUIRED = "水表厂家不能为空";
    String METER_IMPORT_MODEL_NAME_REQUIRED = "水表型号不能为空";
    String METER_IMPORT_CALIBER_NAME_REQUIRED = "水表口径不能为空";
    String METER_IMPORT_RANGE_NAME_REQUIRED = "水表量程不能为空";

    String METER_IMPORT_STEEL_MARK_EXISTS_IN_DB = "钢印号已存在于数据库：{}";
    String METER_IMPORT_BAR_CODE_EXISTS_IN_DB = "条形码已存在于数据库：{}";

    String METER_IMPORT_MAKER_NOT_EXISTS_MSG = "水表厂家不存在或未启用：{}";
    String METER_IMPORT_MODEL_NOT_EXISTS_MSG = "水表型号不存在或未启用：{}";
    String METER_IMPORT_CALIBER_NOT_EXISTS_MSG = "水表口径不存在或未启用：{}";
    String METER_IMPORT_RANGE_NOT_EXISTS_MSG = "水表量程不存在或未启用：{}";
    String METER_IMPORT_TYPE_NOT_EXISTS_MSG = "水表类型不存在：{}";

    // ======================= METER_OUT_IMPORT 水表出库导入 =======================
    
    String METER_OUT_IMPORT_STEEL_MARK_REQUIRED = "钢印号不能为空";
    String METER_OUT_IMPORT_SEAL_NUMBER_REQUIRED = "水表编号不能为空";
    String METER_OUT_IMPORT_STEEL_MARK_NOT_EXISTS = "钢印号不存在：{}";
    String METER_OUT_IMPORT_MISSING_SEAL_NUMBER = "缺少水表编号";

}