package cn.com.emsoft.sw.module.infra.convert.config;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.module.infra.controller.admin.config.vo.*;
import cn.com.emsoft.sw.module.infra.dal.dataobject.config.ConfigDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface ConfigConvert {

    ConfigConvert INSTANCE = Mappers.getMapper(ConfigConvert.class);

    PageResult<ConfigRespVO> convertPage(PageResult<ConfigDO> page);

    List<ConfigRespVO> convertList(List<ConfigDO> list);

    @Mapping(source = "configKey", target = "key")
    ConfigRespVO convert(ConfigDO bean);

    @Mapping(source = "key", target = "configKey")
    ConfigDO convert(ConfigSaveReqVO bean);

    ConfigDO convert(BizConfigCreateReqVO bean);

    ConfigDO convert(BizConfigUpdateReqVO bean);

    List<BizConfigListRespVO> convertToBizConfigList(List<ConfigDO> list);

    BizConfigListRespVO convertToBizConfig(ConfigDO bean);

}
