package cn.com.emsoft.sw.module.infra.service.config;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.module.infra.controller.admin.config.vo.BizConfigCreateReqVO;
import cn.com.emsoft.sw.module.infra.controller.admin.config.vo.BizConfigUpdateReqVO;
import cn.com.emsoft.sw.module.infra.controller.admin.config.vo.ConfigPageReqVO;
import cn.com.emsoft.sw.module.infra.controller.admin.config.vo.ConfigSaveReqVO;
import cn.com.emsoft.sw.module.infra.convert.config.ConfigConvert;
import cn.com.emsoft.sw.module.infra.dal.dataobject.config.ConfigDO;
import cn.com.emsoft.sw.module.infra.dal.mysql.config.ConfigMapper;
import cn.com.emsoft.sw.module.infra.enums.config.ConfigTypeEnum;
import cn.hutool.core.util.ObjUtil;
import com.google.common.annotations.VisibleForTesting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import jakarta.annotation.Resource;

import java.util.List;

import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.com.emsoft.sw.module.infra.enums.ErrorCodeConstants.*;

/**
 * 参数配置 Service 实现类
 */
@Service
@Slf4j
@Validated
public class ConfigServiceImpl implements ConfigService {

    @Resource
    private ConfigMapper configMapper;

    @Override
    public Long createConfig(ConfigSaveReqVO createReqVO) {
        // 校验参数配置 key 的唯一性
        validateConfigKeyUnique(null, createReqVO.getKey());

        // 插入参数配置
        ConfigDO config = ConfigConvert.INSTANCE.convert(createReqVO);
        config.setType(ConfigTypeEnum.CUSTOM.getType());
        configMapper.insert(config);
        return config.getId();
    }

    @Override
    public Long createMeterOrParameterConfig(BizConfigCreateReqVO createReqVO) {
        // 校验参数配置key以及名称在当前category下的唯一性
        validateNameAndConfigKeyUniqueOnSameCategory(null,createReqVO.getName(),createReqVO.getConfigKey(),createReqVO.getCategory());
        // 插入参数配置
        ConfigDO config = ConfigConvert.INSTANCE.convert(createReqVO);
        config.setVisible(Boolean.TRUE);
        configMapper.insert(config);
        return config.getId();
    }

    @Override
    public void updateConfig(ConfigSaveReqVO updateReqVO) {
        // 校验自己存在
        validateConfigExists(updateReqVO.getId());
        // 校验参数配置 key 的唯一性
        validateConfigKeyUnique(updateReqVO.getId(), updateReqVO.getKey());

        // 更新参数配置
        ConfigDO updateObj = ConfigConvert.INSTANCE.convert(updateReqVO);
        configMapper.updateById(updateObj);
    }

    @Override
    public void updateMeterOrParameterConfig(BizConfigUpdateReqVO updateReqVO) {
        // 校验自己存在
        validateConfigExists(updateReqVO.getId());
        // 校验参数配置key以及名称在当前category下的唯一性
        validateNameAndConfigKeyUniqueOnSameCategory(updateReqVO.getId(),updateReqVO.getName(),updateReqVO.getConfigKey(),updateReqVO.getCategory());
        // 更新参数配置
        ConfigDO updateObj = ConfigConvert.INSTANCE.convert(updateReqVO);
        configMapper.updateById(updateObj);
    }

    @Override
    public void deleteConfig(Long id) {
        // 校验配置存在
        ConfigDO config = validateConfigExists(id);
        // 内置配置，不允许删除
        if (ConfigTypeEnum.SYSTEM.getType().equals(config.getType())) {
            throw exception(CONFIG_CAN_NOT_DELETE_SYSTEM_TYPE);
        }
        // 删除
        configMapper.deleteById(id);
    }

    @Override
    public ConfigDO getConfig(Long id) {
        return configMapper.selectById(id);
    }

    @Override
    public ConfigDO getConfigByKey(String key) {
        return configMapper.selectByKey(key);
    }

    @Override
    public PageResult<ConfigDO> getConfigPage(ConfigPageReqVO pageReqVO) {
        return configMapper.selectPage(pageReqVO);
    }

    @Override
    public List<ConfigDO> getConfigListByTypeAndCategory(Integer type, String category) {
        return configMapper.selectByTypeAndCategory(type, category);
    }

    @Override
    public List<String> getCategoriesByType(Integer type) {
        return configMapper.selectCategoriesByType(type);
    }

    @VisibleForTesting
    public ConfigDO validateConfigExists(Long id) {
        if (id == null) {
            return null;
        }
        ConfigDO config = configMapper.selectById(id);
        if (config == null) {
            throw exception(CONFIG_NOT_EXISTS);
        }
        return config;
    }

    @VisibleForTesting
    public void validateConfigKeyUnique(Long id, String key) {
        ConfigDO config = configMapper.selectByKey(key);
        if (config == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的参数配置
        if (id == null) {
            throw exception(CONFIG_KEY_DUPLICATE);
        }
        if (!config.getId().equals(id)) {
            throw exception(CONFIG_KEY_DUPLICATE);
        }
    }

    /**
     * 校验参数名称以及编码唯一
     * @param id 主键
     * @param name 名称
     * @param configKey 编码
     */
    private void validateNameAndConfigKeyUniqueOnSameCategory(Long id,String name,String configKey,String category){
        validateNameUniqueOnSameCategory(id,name,category);
        validateConfigKeyUniqueOnSameCategory(id,configKey,category);
    }
    /**
     * 校验参数名称唯一
     * @param id 主键
     * @param name 名称
     */
    private void validateNameUniqueOnSameCategory(Long id,String name,String category){
        ConfigDO configDO = configMapper.selectOne(ConfigDO::getCategory, category, ConfigDO::getName, name);
        if (configDO == null){
            return;
        }
        if (id == null || ObjUtil.notEqual(id,configDO.getId())){
            throw exception(CONFIG_NAME_ON_CATEGORY_DUPLICATE);
        }
    }
    /**
     * 校验参数编码唯一
     * @param id 主键
     * @param configKey 编码
     */
    private void validateConfigKeyUniqueOnSameCategory(Long id,String configKey,String category){
        ConfigDO configDO = configMapper.selectOne(ConfigDO::getCategory, category, ConfigDO::getConfigKey, configKey);
        if (configDO == null){
            return;
        }
        if (id == null || ObjUtil.notEqual(id,configDO.getId())){
            throw exception(CONFIG_KEY_ON_CATEGORY_DUPLICATE);
        }
    }

}
