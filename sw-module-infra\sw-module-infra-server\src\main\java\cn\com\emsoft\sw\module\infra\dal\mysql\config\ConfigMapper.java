package cn.com.emsoft.sw.module.infra.dal.mysql.config;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.module.infra.controller.admin.config.vo.ConfigPageReqVO;
import cn.com.emsoft.sw.module.infra.dal.dataobject.config.ConfigDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Objects;

@Mapper
public interface ConfigMapper extends BaseMapperX<ConfigDO> {

    default ConfigDO selectByKey(String key) {
        return selectOne(ConfigDO::getConfigKey, key);
    }

    default PageResult<ConfigDO> selectPage(ConfigPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ConfigDO>()
                .likeIfPresent(ConfigDO::getName, reqVO.getName())
                .likeIfPresent(ConfigDO::getConfigKey, reqVO.getKey())
                .eqIfPresent(ConfigDO::getType, reqVO.getType())
                .betweenIfPresent(ConfigDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ConfigDO::getId));
    }

    default List<ConfigDO> selectByTypeAndCategory(Integer type, String category) {
        return selectList(new LambdaQueryWrapperX<ConfigDO>()
                .eq(ConfigDO::getType, type)
                .eq(ConfigDO::getCategory, category)
                .orderByDesc(ConfigDO::getId));
    }

    default List<String> selectCategoriesByType(Integer type) {
        return selectList(new LambdaQueryWrapperX<ConfigDO>()
                .select(ConfigDO::getCategory)
                .eq(ConfigDO::getType, type)
                .groupBy(ConfigDO::getCategory)
                .orderByAsc(ConfigDO::getCategory))
                .stream()
                .map(ConfigDO::getCategory)
                .filter(Objects::nonNull)
                .toList();
    }

}
