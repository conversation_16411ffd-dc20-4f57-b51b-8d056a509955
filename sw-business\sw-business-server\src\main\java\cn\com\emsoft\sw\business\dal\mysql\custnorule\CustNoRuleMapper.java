package cn.com.emsoft.sw.business.dal.mysql.custnorule;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.custnorule.vo.CustNoRulePageReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.custnorule.CustNoRuleDO;
import cn.com.emsoft.sw.business.dal.dataobject.custnorule.CustNoRuleDORef;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;


/**
 * 户号规则 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CustNoRuleMapper extends BaseMapperX<CustNoRuleDO> {

    default PageResult<CustNoRuleDO> selectPage(CustNoRulePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CustNoRuleDO>()
                // 多字段模糊和排序
                .getSearchAndSort(CustNoRuleDORef.class, reqVO.getSearch(), reqVO.getSearchContent(), reqVO.getSort())
                .likeIfPresent(CustNoRuleDO::getCode, reqVO.getCode())
                .orderByDesc(CustNoRuleDO::getId));
    }

}