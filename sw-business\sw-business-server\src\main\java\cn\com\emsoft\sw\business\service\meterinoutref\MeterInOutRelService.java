package cn.com.emsoft.sw.business.service.meterinoutref;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.meterinoutref.vo.MeterInOutRelPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterinoutref.vo.MeterInOutRelSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinoutref.MeterInOutRelDO;
import jakarta.validation.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;


/**
 * 水表出/入库详情 Service 接口
 *
 * <AUTHOR>
 */
public interface MeterInOutRelService {

    /**
     * 创建水表出/入库详情
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMeterInOutRef(@Valid MeterInOutRelSaveReqVO createReqVO);

    /**
     * 更新水表出/入库详情
     *
     * @param updateReqVO 更新信息
     */
    void updateMeterInOutRef(@Valid MeterInOutRelSaveReqVO updateReqVO);

    /**
     * 删除水表出/入库详情
     *
     * @param id 编号
     */
    void deleteMeterInOutRef(Long id);

    /**
    * 批量删除水表出/入库详情
    *
    * @param ids 编号
    */
    void deleteMeterInOutRefListByIds(List<Long> ids);

    /**
     * 获得水表出/入库详情
     *
     * @param id 编号
     * @return 水表出/入库详情
     */
    MeterInOutRelDO getMeterInOutRef(Long id);

    /**
     * 获得水表出/入库详情分页
     *
     * @param pageReqVO 分页查询
     * @return 水表出/入库详情分页
     */
    PageResult<MeterInOutRelDO> getMeterInOutRefPage(MeterInOutRelPageReqVO pageReqVO);

}