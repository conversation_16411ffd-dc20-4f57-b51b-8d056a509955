package cn.com.emsoft.sw.business.service.invoicetaxrate;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxrateCreateReqVO;
import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxratePageReqVO;
import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxrateRespVO;
import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxrateUpdateReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.invoicetaxrate.InvoiceTaxrateDO;
import jakarta.validation.*;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;


/**
 * 发票税率 Service 接口
 *
 * <AUTHOR>
 */
public interface InvoiceTaxrateService {

    /**
     * 创建发票税率
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInvoiceTaxrate(@Valid InvoiceTaxrateCreateReqVO createReqVO);

    /**
     * 更新发票税率
     *
     * @param updateReqVO 更新信息
     */
    void updateInvoiceTaxrate(@Valid InvoiceTaxrateUpdateReqVO updateReqVO);

    /**
     * 删除发票税率
     *
     * @param id 编号
     */
    void deleteInvoiceTaxrate(Long id);

    /**
    * 批量删除发票税率
    *
    * @param ids 编号
    */
    void deleteInvoiceTaxrateListByIds(List<Long> ids);

    /**
     * 获得发票税率
     *
     * @param id 编号
     * @return 发票税率
     */
    InvoiceTaxrateDO getInvoiceTaxrate(Long id);

    /**
     * 获得发票税率分页
     *
     * @param pageReqVO 分页查询
     * @return 发票税率分页
     */
    PageResult<InvoiceTaxrateRespVO> getInvoiceTaxratePage(InvoiceTaxratePageReqVO pageReqVO);

}