package cn.com.emsoft.sw.business.service.deptcustnorulerel;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel.vo.DeptCustNoRuleRelPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel.vo.DeptCustNoRuleRelSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.deptcustnorulerel.DeptCustNoRuleRelDO;
import jakarta.validation.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;

/**
 * 户号规则和字典关系 Service 接口
 *
 * <AUTHOR>
 */
public interface DeptCustNoRuleRelService {

    /**
     * 创建户号规则和字典关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDeptCustNoRuleRel(@Valid DeptCustNoRuleRelSaveReqVO createReqVO);

    /**
     * 更新户号规则和字典关系
     *
     * @param updateReqVO 更新信息
     */
    void updateDeptCustNoRuleRel(@Valid DeptCustNoRuleRelSaveReqVO updateReqVO);

    /**
     * 删除户号规则和字典关系
     *
     * @param id 编号
     */
    void deleteDeptCustNoRuleRel(Long id);

    /**
    * 批量删除户号规则和字典关系
    *
    * @param ids 编号
    */
    void deleteDeptCustNoRuleRelListByIds(List<Long> ids);

    /**
     * 获得户号规则和字典关系
     *
     * @param id 编号
     * @return 户号规则和字典关系
     */
    DeptCustNoRuleRelDO getDeptCustNoRuleRel(Long id);

    /**
     * 获得户号规则和字典关系分页
     *
     * @param pageReqVO 分页查询
     * @return 户号规则和字典关系分页
     */
    PageResult<DeptCustNoRuleRelDO> getDeptCustNoRuleRelPage(DeptCustNoRuleRelPageReqVO pageReqVO);

}