package cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel;

import cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel.vo.DeptCustNoRuleRelPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel.vo.DeptCustNoRuleRelRespVO;
import cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel.vo.DeptCustNoRuleRelSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.deptcustnorulerel.DeptCustNoRuleRelDO;
import cn.com.emsoft.sw.business.service.deptcustnorulerel.DeptCustNoRuleRelService;
import cn.com.emsoft.sw.framework.common.pojo.PageParam;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.constraints.*;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.pojo.CommonResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;
import static cn.com.emsoft.sw.framework.common.pojo.CommonResult.success;

import cn.com.emsoft.sw.framework.excel.core.util.ExcelUtils;

import cn.com.emsoft.sw.framework.apilog.core.annotation.ApiAccessLog;
import static cn.com.emsoft.sw.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 户号规则和字典关系")
@RestController
@RequestMapping("/business/dept-cust-no-rule-rel")
@Validated
public class DeptCustNoRuleRelController {

    @Resource
    private DeptCustNoRuleRelService deptCustNoRuleRelService;

//    @PostMapping("/create")
//    @Operation(summary = "创建户号规则和字典关系")
//    @PreAuthorize("@ss.hasPermission('business:dept-cust-no-rule-rel:create')")
    public CommonResult<Long> createDeptCustNoRuleRel(@Valid @RequestBody DeptCustNoRuleRelSaveReqVO createReqVO) {
        return success(deptCustNoRuleRelService.createDeptCustNoRuleRel(createReqVO));
    }

//    @PutMapping("/update")
//    @Operation(summary = "更新户号规则和字典关系")
//    @PreAuthorize("@ss.hasPermission('business:dept-cust-no-rule-rel:update')")
    public CommonResult<Boolean> updateDeptCustNoRuleRel(@Valid @RequestBody DeptCustNoRuleRelSaveReqVO updateReqVO) {
        deptCustNoRuleRelService.updateDeptCustNoRuleRel(updateReqVO);
        return success(true);
    }

//    @DeleteMapping("/delete")
//    @Operation(summary = "删除户号规则和字典关系")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('business:dept-cust-no-rule-rel:delete')")
    public CommonResult<Boolean> deleteDeptCustNoRuleRel(@RequestParam("id") Long id) {
        deptCustNoRuleRelService.deleteDeptCustNoRuleRel(id);
        return success(true);
    }

//    @DeleteMapping("/delete-list")
//    @Parameter(name = "ids", description = "编号", required = true)
//    @Operation(summary = "批量删除户号规则和字典关系")
//    @PreAuthorize("@ss.hasPermission('business:dept-cust-no-rule-rel:delete')")
    public CommonResult<Boolean> deleteDeptCustNoRuleRelList(@RequestParam("ids") List<Long> ids) {
        deptCustNoRuleRelService.deleteDeptCustNoRuleRelListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得户号规则和字典关系")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:dept-cust-no-rule-rel:query')")
    public CommonResult<DeptCustNoRuleRelRespVO> getDeptCustNoRuleRel(@RequestParam("id") Long id) {
        DeptCustNoRuleRelDO deptCustNoRuleRel = deptCustNoRuleRelService.getDeptCustNoRuleRel(id);
        return success(BeanUtils.toBean(deptCustNoRuleRel, DeptCustNoRuleRelRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得户号规则和字典关系分页")
    @PreAuthorize("@ss.hasPermission('business:dept-cust-no-rule-rel:query')")
    public CommonResult<PageResult<DeptCustNoRuleRelRespVO>> getDeptCustNoRuleRelPage(@Valid DeptCustNoRuleRelPageReqVO pageReqVO) {
        PageResult<DeptCustNoRuleRelDO> pageResult = deptCustNoRuleRelService.getDeptCustNoRuleRelPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DeptCustNoRuleRelRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出户号规则和字典关系 Excel")
    @PreAuthorize("@ss.hasPermission('business:dept-cust-no-rule-rel:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportDeptCustNoRuleRelExcel(@Valid DeptCustNoRuleRelPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<DeptCustNoRuleRelDO> list = deptCustNoRuleRelService.getDeptCustNoRuleRelPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "户号规则和字典关系.xls", "数据", DeptCustNoRuleRelRespVO.class,
                        BeanUtils.toBean(list, DeptCustNoRuleRelRespVO.class));
    }

}