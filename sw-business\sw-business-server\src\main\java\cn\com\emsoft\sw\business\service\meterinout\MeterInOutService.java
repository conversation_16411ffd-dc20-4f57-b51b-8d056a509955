package cn.com.emsoft.sw.business.service.meterinout;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.*;
import cn.com.emsoft.sw.business.dal.dataobject.meterinout.MeterInOutDO;
import jakarta.validation.*;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;

/**
 * 水表出/入库单 Service 接口
 *
 * <AUTHOR>
 */
public interface MeterInOutService {

    /**
     * 创建水表出/入库单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMeterInOut(@Valid MeterInOutSaveReqVO createReqVO);

    /**
     * 更新水表出/入库单
     *
     * @param updateReqVO 更新信息
     */
    void updateMeterInOut(@Valid MeterInOutSaveReqVO updateReqVO);

    /**
     * 删除水表出/入库单
     *
     * @param id 编号
     */
    void deleteMeterInOut(Long id);

    /**
     * 批量删除水表出/入库单
     *
     * @param ids 编号
     */
    void deleteMeterInOutListByIds(List<Long> ids);

    /**
     * 获得水表出/入库单
     *
     * @param id 编号
     * @return 水表出/入库单
     */
    MeterInOutDO getMeterInOut(Long id);

    /**
     * 获得水表出/入库单分页
     *
     * @param pageReqVO 分页查询
     * @return 水表出/入库单分页
     */
    PageResult<MeterInOutDO> getMeterInOutPage(MeterInOutPageReqVO pageReqVO);

    /**
     * 获得水表出/入库单分页（增强版）
     * 包含水表数量统计、部门名称、库存类型名称等业务信息
     *
     * @param pageReqVO 分页查询
     * @return 水表出/入库单分页响应（包含业务字段）
     */
    PageResult<MeterInOutPageRespVO> getMeterInOutPageWithBusiness(MeterInOutPageReqVO pageReqVO);

    /**
     * 校验水表导入数据
     * 注意：此方法只进行数据校验，不执行实际的数据插入操作
     *
     * @param importList Excel导入的数据列表
     * @return 校验结果，包含每条数据的校验详情
     */
    MeterImportRespVO checkMeterImportList(@Valid List<MeterImportVO> importList);

    /**
     * 校验水表出库导入数据
     * 注意：此方法只进行数据校验，不执行实际的出库操作
     *
     * @param importList Excel导入的出库数据列表
     * @return 校验结果，包含每条数据的校验详情
     */
    MeterImportRespVO checkMeterOutImportList(@Valid List<MeterOutImportVO> importList);

    /**
     * 获得水表出/入库单详情
     * 
     * 功能说明：
     * - 查询指定出/入库单关联的所有水表数据
     * - 按厂家代码、型号代码、口径代码、量程代码、类型编码进行分组聚合
     * - 统计每组的数量并设置到count字段
     * - 批量查询各种名称信息并填充到VO
     * 
     * @param id 出/入库单ID
     * @return 聚合后的水表详情列表
     */
    List<MeterInOutDetailsRespVO> getMeterInOutDetails(Long id);

    /**
     * 获得水表出/入库单分页明细
     * 
     * 功能说明：
     * - 根据查询条件过滤出/入库单数据
     * - 关联查询对应的水表信息
     * - 批量查询相关字典数据(厂家、型号、口径、量程)
     * - 组装返回数据并分页
     * 
     * @param pageReqVO 分页查询条件
     * @return 分页结果
     */
    PageResult<MeterInOutPageDetailsRespVO> getMeterInOutPageDetails(MeterInOutPageDetailsReqVO pageReqVO);

    /**
     * 水表入库
     *
     * 功能说明：
     * - 执行二次业务校验（不信任前端数据）
     * - 创建入库单记录（自动生成入库单编号）
     * - 批量插入水表信息到水表主表
     * - 支持单一数据入库和批量入库两种模式
     *
     * @param createVO 水表入库请求数据
     * @return 入库单ID
     */
    Long meterIn(@Valid MeterInCreateVO createVO);

    /**
     * 水表出库
     *
     * 功能说明：
     * - 支持普通出库和调库两种模式
     * - 普通出库：更新水表状态为出库状态，记录出库日志
     * - 调库出库：先出库再入库到目标仓库，记录出库和入库日志
     * - 执行严格的业务校验（水表存在性、状态、钢印号等）
     * - 调库时校验目标仓库钢印号唯一性
     *
     * @param createVO 水表出库请求数据
     * @return 出库单ID
     */
    Long meterOut(@Valid MeterOutCreateVO createVO);

}