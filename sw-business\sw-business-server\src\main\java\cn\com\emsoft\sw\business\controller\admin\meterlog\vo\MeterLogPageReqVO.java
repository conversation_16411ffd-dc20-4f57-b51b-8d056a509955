package cn.com.emsoft.sw.business.controller.admin.meterlog.vo;

import cn.com.emsoft.sw.framework.common.pojo.SearchAndSortPageParam;
import cn.com.emsoft.sw.framework.common.validation.GetterRef;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "管理后台 - 表务日志分页 Request VO")
@Data
@GetterRef
public class MeterLogPageReqVO extends SearchAndSortPageParam {

    @Schema(description = "条形码")
    private String barCode;

    @Schema(description = "钢印号")
    private String steelMark;

//    以下字段不唯一，先注释，防止报错
//    @Schema(description = "水表编号")
//    private String sealNumber;

//    @Schema(description = "强检编号")
//    private String checkCode;
}