package cn.com.emsoft.sw.module.infra.enums.config;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ConfigTypeEnum {

    /**
     * 系统配置
     */
    SYSTEM(1),
    /**
     * 自定义配置
     */
    CUSTOM(2),
    /**
     * 抄表参数
     */
    METER_READING_PARAMETERS(10),
    /**
     * 参数配置
     */
    PARAMETER_CONFIGURATION(20);



    private final Integer type;

}
