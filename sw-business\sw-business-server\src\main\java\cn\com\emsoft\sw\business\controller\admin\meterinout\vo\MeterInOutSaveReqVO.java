package cn.com.emsoft.sw.business.controller.admin.meterinout.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 水表出/入库单新增/修改 Request VO")
@Data
public class MeterInOutSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2807")
    private Long id;

    @Schema(description = "营业站点", requiredMode = Schema.RequiredMode.REQUIRED, example = "14736")
    @NotNull(message = "营业站点不能为空")
    private Long deptId;

    @Schema(description = "采购单编号")
    private String payCode;

    @Schema(description = "库存单编号，RK/CK + 6位随机", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "库存单编号，RK/CK + 6位随机不能为空")
    private String stockCode;

    @Schema(description = "库存单类型，1=入库，2=出库", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "库存单类型，1=入库，2=出库不能为空")
    private Short stockType;

    @Schema(description = "出/入库人，记入用户名", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "出/入库人，记入用户名不能为空")
    private String operationUser;

    @Schema(description = "出/入库时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "出/入库时间不能为空")
    private LocalDateTime operationTime;

    @Schema(description = "备注", example = "随便")
    private String remark;

}