package cn.com.emsoft.sw.business.service.meter;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.meter.vo.*;
import cn.com.emsoft.sw.business.dal.dataobject.meter.MeterDO;
import jakarta.validation.*;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;

/**
 * 水表信息 Service 接口
 *
 * <AUTHOR>
 */
public interface MeterService {

    /**
     * 创建水表信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMeter(@Valid MeterSaveReqVO createReqVO);

    /**
     * 更新水表信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMeter(@Valid MeterUpdateReqVO updateReqVO);

    /**
     * 更新水表钢印号、水表编号
     *
     * @param updateReqVO 更新信息
     */
    void updateMeterSteelMarkAndSealNumber(@Valid MeterUpdateSpecialInfoReqVO updateReqVO);

    /**
     * 删除水表信息
     *
     * @param id 编号
     */
    void deleteMeter(Long id);

    /**
     * 批量删除水表信息
     *
     * @param ids 编号
     */
    void deleteMeterListByIds(List<Long> ids);

    /**
     * 获得水表信息
     *
     * @param id 编号
     * @return 水表信息
     */
    MeterDO getMeter(Long id);

    /**
     * 获得水表信息
     *
     * @param reqVO 多条件查询
     * @return 水表信息
     */
    MeterDO getMeterDetails(MeterDetailsReqVO reqVO);

    /**
     * 获得水表信息分页（包含详细信息）
     * 根据部门ID查询相关的出入库单，然后查询关联的水表信息
     *
     * @param pageReqVO 分页查询条件
     * @return 水表信息分页结果，包含厂家、型号、口径、量程等详细信息
     */
    PageResult<MeterPageRespVO> getMeterPageWithDetails(MeterPageReqVO pageReqVO);

}