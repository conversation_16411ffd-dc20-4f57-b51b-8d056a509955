package cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 发票税率新增 Request VO")
@Data
public class InvoiceTaxrateCreateReqVO {

    @Schema(description = "销售方/水司账户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11378")
    @NotNull(message = "销售方/水司账户id不能为空")
    private Long accountId;

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "编码不能为空")
    private String code;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @NotEmpty(message = "名称不能为空")
    private String name;

    @Schema(description = "商品编码（唯一）")
    private String productCode;

    @Schema(description = "零税率标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "零税率标识不能为空")
    private String taxRateMark;

    @Schema(description = "税率百分比", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "税率百分比不能为空")
    @Max(value = 100,message = "请输入小于100的数字")
    @Min(value = 0,message = "请输入大于0的数字")
    private BigDecimal taxRate;

    @Schema(description = "项目单位")
    private String projectUnit;

    @Schema(description = "规格型号")
    private String specificationType;

    @Schema(description = "开票类型（多值，逗号分隔）", example = "2")
    @Pattern(regexp = "^\\d+(,\\d+)*$", message = "格式必须为逗号分隔的数字，如：1,2,3,4")
    private String invoiceType;

}