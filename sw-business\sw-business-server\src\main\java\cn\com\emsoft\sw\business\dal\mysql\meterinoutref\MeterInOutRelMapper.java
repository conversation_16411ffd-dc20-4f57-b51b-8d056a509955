package cn.com.emsoft.sw.business.dal.mysql.meterinoutref;

import java.util.*;
import java.util.stream.Collectors;

import cn.com.emsoft.sw.business.controller.admin.meterinoutref.vo.MeterInOutRelPageReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinoutref.MeterInOutRelDO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinoutref.MeterInOutRelDORef;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;

import org.apache.ibatis.annotations.Mapper;


/**
 * 水表出/入库详情 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MeterInOutRelMapper extends BaseMapperX<MeterInOutRelDO> {

    default PageResult<MeterInOutRelDO> selectPage(MeterInOutRelPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MeterInOutRelDO>()
                // 多字段模糊和排序
                .getSearchAndSort(MeterInOutRelDORef.class, reqVO.getSearch(), reqVO.getSearchContent(), reqVO.getSort())
                .eqIfPresent(MeterInOutRelDO::getMeterInOutId, reqVO.getMeterInOutId())
                .eqIfPresent(MeterInOutRelDO::getMeterId, reqVO.getMeterId())
                .eqIfPresent(MeterInOutRelDO::getRemark, reqVO.getRemark())
                .eqIfPresent(MeterInOutRelDO::getStatus, reqVO.getStatus())
                .betweenIfPresent(MeterInOutRelDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(MeterInOutRelDO::getId));
    }

    /**
     * 根据出入库单ID查询关联的水表ID列表
     *
     * @param inOutIds 出入库单ID集合
     * @return 水表ID列表
     */
    default List<Long> selectMeterIdsByInOutIds(Collection<Long> inOutIds) {
        if (inOutIds == null || inOutIds.isEmpty()) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapperX<MeterInOutRelDO>()
                .select(MeterInOutRelDO::getMeterId)
                .in(MeterInOutRelDO::getMeterInOutId, inOutIds))
                .stream()
                .map(MeterInOutRelDO::getMeterId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
    }

    /**
     * 根据水表ID查询关联的出入库单ID列表
     *
     * @param meterIds 水表ID集合
     * @return 出入库单ID列表
     */
    default List<Long> selectInOutIdsByMeterIds(Collection<Long> meterIds) {
        if (meterIds == null || meterIds.isEmpty()) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapperX<MeterInOutRelDO>()
                .select(MeterInOutRelDO::getMeterInOutId)
                .in(MeterInOutRelDO::getMeterId, meterIds))
                .stream()
                .map(MeterInOutRelDO::getMeterInOutId)
                .filter(Objects::nonNull)
                .distinct()
                .toList();
    }

    /**
     * 根据出入库单ID查询关联的水表ID列表（单个）
     *
     * @param inOutId 出入库单ID
     * @return 水表ID列表
     */
    default List<Long> selectMeterIdsByInOutId(Long inOutId) {
        if (inOutId == null) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapperX<MeterInOutRelDO>()
                .select(MeterInOutRelDO::getMeterId)
                .eq(MeterInOutRelDO::getMeterInOutId, inOutId))
                .stream()
                .map(MeterInOutRelDO::getMeterId)
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 统计出入库单关联的水表数量
     *
     * @param inOutIds 出入库单ID集合
     * @return 出入库单ID与水表数量的映射关系
     */
    default Map<Long, Long> selectMeterCountByInOutIds(Collection<Long> inOutIds) {
        if (inOutIds == null || inOutIds.isEmpty()) {
            return Collections.emptyMap();
        }
        
        List<MeterInOutRelDO> refList = selectList(new LambdaQueryWrapperX<MeterInOutRelDO>()
                .select(MeterInOutRelDO::getMeterInOutId)
                .in(MeterInOutRelDO::getMeterInOutId, inOutIds));
        
        return refList.stream()
                .collect(Collectors.groupingBy(
                    MeterInOutRelDO::getMeterInOutId,
                    Collectors.counting()
                ));
    }

    /**
     * 批量插入出入库单与水表的关联关系
     *
     * @param inOutId 出入库单ID
     * @param meterIds 水表ID列表
     */
    default void insertBatchMeterInOutRefs(Long inOutId, Collection<Long> meterIds) {
        if (inOutId == null || meterIds == null || meterIds.isEmpty()) {
            return;
        }
        
        List<MeterInOutRelDO> refList = meterIds.stream()
                .filter(Objects::nonNull)
                .map(meterId -> MeterInOutRelDO.builder()
                        .meterInOutId(inOutId)
                        .meterId(meterId)
                        .status((short) 0)
                        .build())
                .toList();
        
        if (!refList.isEmpty()) {
            insertBatch(refList);
        }
    }

    /**
     * 根据出入库单ID删除关联关系
     *
     * @param inOutId 出入库单ID
     */
    default void deleteByInOutId(Long inOutId) {
        if (inOutId == null) {
            return;
        }
        delete(new LambdaQueryWrapperX<MeterInOutRelDO>()
                .eq(MeterInOutRelDO::getMeterInOutId, inOutId));
    }

}