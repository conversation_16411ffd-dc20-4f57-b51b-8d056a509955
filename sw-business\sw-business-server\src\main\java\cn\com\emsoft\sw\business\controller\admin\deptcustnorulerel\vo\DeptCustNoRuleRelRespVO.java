package cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 户号规则和字典关系 Response VO")
@Data
@ExcelIgnoreUnannotated
public class DeptCustNoRuleRelRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19564")
    @ExcelProperty("主键")
    private Long id;

    @Schema(description = "部门代码，关联系统部门表", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("部门代码，关联系统部门表")
    private String deptCode;

    @Schema(description = "户号规则id，关联户号规则表", requiredMode = Schema.RequiredMode.REQUIRED, example = "31376")
    @ExcelProperty("户号规则id，关联户号规则表")
    private Long custRuleId;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "状态：0-否，1-是", example = "1")
    @ExcelProperty("状态：0-否，1-是")
    private Short status;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人id")
    @ExcelProperty("创建人id")
    private String creator;

    @Schema(description = "更新人id")
    @ExcelProperty("更新人id")
    private String updater;

    @Schema(description = "是否删除")
    @ExcelProperty("是否删除")
    private Short deleted;

    @Schema(description = "租户id", example = "7399")
    @ExcelProperty("租户id")
    private Long tenantId;

}