package cn.com.emsoft.sw.business.service.custnorule;

import cn.com.emsoft.sw.business.controller.admin.custnorule.vo.CustNoRuleCreateReqVO;
import cn.com.emsoft.sw.business.controller.admin.custnorule.vo.CustNoRulePageReqVO;
import cn.com.emsoft.sw.business.controller.admin.custnorule.vo.CustNoRuleUpdateReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.custnorule.CustNoRuleDO;
import cn.com.emsoft.sw.business.dal.mysql.custnorule.CustNoRuleMapper;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;

import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;

import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.*;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.com.emsoft.sw.framework.common.util.collection.CollectionUtils.convertList;

/**
 * 户号规则 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class CustNoRuleServiceImpl implements CustNoRuleService {

    @Resource
    private CustNoRuleMapper custNoRuleMapper;

    @Override
    public Long createCustNoRule(CustNoRuleCreateReqVO createReqVO) {

        //校验规则编号是否存在
        validateCustNoRuleCodeUnique(null,createReqVO.getCode());
        // 插入
        CustNoRuleDO custNoRule = BeanUtils.toBean(createReqVO, CustNoRuleDO.class);
        custNoRuleMapper.insert(custNoRule);
        // 返回
        return custNoRule.getId();
    }

    @Override
    public void updateCustNoRule(CustNoRuleUpdateReqVO updateReqVO) {
        // 校验存在
        validateCustNoRuleExists(updateReqVO.getId());
        // 更新
        CustNoRuleDO updateObj = BeanUtils.toBean(updateReqVO, CustNoRuleDO.class);
        custNoRuleMapper.updateById(updateObj);
    }

    @Override
    public void deleteCustNoRule(Long id) {
        // 校验存在
        validateCustNoRuleExists(id);
        // 删除
        custNoRuleMapper.deleteById(id);
    }

    @Override
        public void deleteCustNoRuleListByIds(List<Long> ids) {
        // 校验存在
        validateCustNoRuleExists(ids);
        // 删除
        custNoRuleMapper.deleteByIds(ids);
        }

    private void validateCustNoRuleExists(List<Long> ids) {
        List<CustNoRuleDO> list = custNoRuleMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(CUST_NO_RULE_NOT_EXISTS);
        }
    }

    private void validateCustNoRuleExists(Long id) {
        if (custNoRuleMapper.selectById(id) == null) {
            throw exception(CUST_NO_RULE_NOT_EXISTS);
        }
    }

    @Override
    public CustNoRuleDO getCustNoRule(Long id) {
        return custNoRuleMapper.selectById(id);
    }

    @Override
    public PageResult<CustNoRuleDO> getCustNoRulePage(CustNoRulePageReqVO pageReqVO) {
        return custNoRuleMapper.selectPage(pageReqVO);
    }

    /**
     * 校验户号规则编号是否存在
     * @param id 规则id
     * @param code 规则编号
     */
    private void validateCustNoRuleCodeUnique(Long id,String code){
        CustNoRuleDO custNoRuleDO = custNoRuleMapper.selectOne(CustNoRuleDO::getCode, code);
        if (custNoRuleDO == null){
            return;
        }

        //存在记录的情况下
        if (id == null || ObjUtil.notEqual(id,custNoRuleDO.getId())){
            throw exception(CUST_NO_RULE_CODE_EXISTS);
        }
    }

}