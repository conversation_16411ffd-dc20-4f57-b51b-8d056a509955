package cn.com.emsoft.sw.business.controller.admin.meterinoutref.vo;

import cn.com.emsoft.sw.framework.common.pojo.SearchAndSortPageParam;
import cn.com.emsoft.sw.framework.common.validation.GetterRef;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.com.emsoft.sw.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 水表出/入库详情分页 Request VO")
@Data
@GetterRef
public class MeterInOutRelPageReqVO extends SearchAndSortPageParam {

    @Schema(description = "水表出/入库单id，关联水表出入库单", example = "7650")
    private Long meterInOutId;

    @Schema(description = "水表id，关联水表信息表", example = "9067")
    private Long meterId;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "状态：0-否，1-是", example = "1")
    private Short status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}