package cn.com.emsoft.sw.business.controller.admin.meterinout.vo;

import cn.com.emsoft.sw.business.enums.DictTypeConstants;
import cn.com.emsoft.sw.framework.excel.core.annotations.DictFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Schema(description = "管理后台 - 水表出/入库单详情 Response VO")
@Data
public class MeterInOutDetailsRespVO {
    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "16877")
    private Long id;

    @Schema(description = "水表厂家代码，关联水表厂家表", requiredMode = Schema.RequiredMode.REQUIRED)
    private String makerCode;

    @Schema(description = "水表厂家名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String makerName;

    @Schema(description = "水表型号代码，关联水表型号表")
    private String modelCode;

    @Schema(description = "水表型号名称")
    private String modelName;

    @Schema(description = "水表口径代码，关联水表口径表", requiredMode = Schema.RequiredMode.REQUIRED)
    private String caliberCode;

    @Schema(description = "水表口径名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String caliberName;

    @Schema(description = "水表量程代码，关联水表量程表", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rangeCode;

    @Schema(description = "水表量程名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String rangeName;

    @Schema(description = "水表类型编码", example = "1")
    private Integer type;

    @Schema(description = "水表类型名称", example = "普通表")
    @DictFormat(DictTypeConstants.METER_OTH_TYPE)
    private String typeName;

    @Schema(description = "数量", example = "1")
    private Integer count;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}
