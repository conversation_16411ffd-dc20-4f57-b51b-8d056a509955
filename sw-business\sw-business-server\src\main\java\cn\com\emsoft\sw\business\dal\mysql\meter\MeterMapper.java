package cn.com.emsoft.sw.business.dal.mysql.meter;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.meter.vo.MeterDetailsReqVO;
import cn.com.emsoft.sw.business.controller.admin.meter.vo.MeterPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.MeterInOutPageDetailsReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meter.MeterDO;
import cn.com.emsoft.sw.business.dal.dataobject.meter.MeterDORef;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.METER_IN_OUT_STOCK_TYPE_ERROR;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 水表信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MeterMapper extends BaseMapperX<MeterDO> {

    default PageResult<MeterDO> selectPage(MeterPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MeterDO>()
                        // 多字段模糊和排序
                        .getSearchAndSort(MeterDORef.class, reqVO.getSearch(), reqVO.getSearchContent(), reqVO.getSort())
                        .eqIfPresent(MeterDO::getDeptId,reqVO.getDeptId())
                        .eqIfPresent(MeterDO::getSteelMark,reqVO.getSteelMark())
                        .likeIfPresent(MeterDO::getSealNumber,reqVO.getSealNumber())
                        .eqIfPresent(MeterDO::getCheckCode,reqVO.getCheckCode())
                        .eqIfPresent(MeterDO::getMeterStatus,reqVO.getMeterStatus())
                        .eqIfPresent(MeterDO::getMakerCode,reqVO.getMakerCode())
                        .eqIfPresent(MeterDO::getModelCode,reqVO.getModelCode())
                        .eqIfPresent(MeterDO::getCaliberCode,reqVO.getCaliberCode())
                        .eqIfPresent(MeterDO::getRangeCode,reqVO.getRangeCode())
                        .eqIfPresent(MeterDO::getType,reqVO.getType())
                        .eqIfPresent(MeterDO::getCustCode,reqVO.getCustCode())
                        .eqIfPresent(MeterDO::getProjectCode,reqVO.getProjectCode())
                        .orderByDesc(MeterDO::getSteelMark)
               );
    }

    default MeterDO getMeterDetails(MeterDetailsReqVO reqVO){
        return selectOne(new LambdaQueryWrapperX<MeterDO>()
                .eqIfPresent(MeterDO::getId,reqVO.getId())
                .eqIfPresent(MeterDO::getBarCode,reqVO.getBarCode())
                .eqIfPresent(MeterDO::getSteelMark,reqVO.getSteelMark())
                //水表编号和强检编号不唯一，先注释，防止报错，具体等产品
//                .eqIfPresent(MeterDO::getSealNumber,reqVO.getSteelMark())
//                .eqIfPresent(MeterDO::getCheckCode,reqVO.getCheckCode())
        );
    }

    /**
     * 根据水表ID列表和查询条件分页查询水表信息
     * 注意：此方法现在需要传入通过关联表查询得到的水表ID列表
     *
     * @param pageReqVO 分页查询条件
     * @param meterIds 水表ID列表
     * @return 分页查询结果
     */
    default PageResult<MeterDO> selectPageByMeterIds(MeterPageReqVO pageReqVO, List<Long> meterIds) {
        if (meterIds == null || meterIds.isEmpty()) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }
        
        LambdaQueryWrapperX<MeterDO> queryWrapper = new LambdaQueryWrapperX<>();
        
        // 通过水表ID列表查询
        queryWrapper.in(MeterDO::getId, meterIds);
        
        // 添加其他查询条件
        queryWrapper
                // 多字段模糊和排序
                .getSearchAndSort(MeterDORef.class, pageReqVO.getSearch(), pageReqVO.getSearchContent(), pageReqVO.getSort())
                .likeIfPresent(MeterDO::getSteelMark, pageReqVO.getSteelMark())
                .likeIfPresent(MeterDO::getCheckCode, pageReqVO.getCheckCode())
                .eqIfPresent(MeterDO::getMeterStatus, pageReqVO.getMeterStatus())
                .eqIfPresent(MeterDO::getMakerCode, pageReqVO.getMakerCode())
                .eqIfPresent(MeterDO::getModelCode, pageReqVO.getModelCode())
                .eqIfPresent(MeterDO::getCaliberCode, pageReqVO.getCaliberCode())
                .eqIfPresent(MeterDO::getRangeCode, pageReqVO.getRangeCode())
                .eqIfPresent(MeterDO::getType, pageReqVO.getType())
                .eqIfPresent(MeterDO::getCustCode, pageReqVO.getCustCode())
                .eqIfPresent(MeterDO::getProjectCode, pageReqVO.getProjectCode())
                .orderByDesc(MeterDO::getId);
        
        return selectPage(pageReqVO, queryWrapper);
    }

    /**
     * 批量查询钢印号是否存在
     *
     * @param steelMarks 钢印号集合
     * @return 数据库中已存在的钢印号列表
     */
    default List<String> selectExistingSteelMarks(@Param("steelMarks") Set<String> steelMarks) {
        if (steelMarks == null || steelMarks.isEmpty()) {
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapperX<MeterDO>()
                .select(MeterDO::getSteelMark)
                .in(MeterDO::getSteelMark, steelMarks))
                .stream()
                .map(MeterDO::getSteelMark)
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 批量查询条形码是否存在
     *
     * @param barCodes 条形码集合
     * @return 数据库中已存在的条形码列表
     */
    default List<String> selectExistingBarCodes(@Param("barCodes") Set<String> barCodes) {
        if (barCodes == null || barCodes.isEmpty()) {
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapperX<MeterDO>()
                .select(MeterDO::getBarCode)
                .in(MeterDO::getBarCode, barCodes))
                .stream()
                .map(MeterDO::getBarCode)
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * 根据水表ID列表查询水表信息（用于通过关联表获取的水表ID）
     *
     * @param meterIds 水表ID集合
     * @return 水表信息列表
     */
    default List<MeterDO> selectListByIds(Collection<Long> meterIds) {
        if (meterIds == null || meterIds.isEmpty()) {
            return Collections.emptyList();
        }
        return selectList(new LambdaQueryWrapperX<MeterDO>()
                .in(MeterDO::getId, meterIds));
    }

    /**
     * 根据水表ID列表分页查询水表详情信息
     * 注意：此方法现在需要传入通过关联表查询得到的水表ID列表
     *
     * @param pageReqVO 分页查询条件
     * @param meterIds 水表ID列表
     * @return 分页查询结果
     */
    default PageResult<MeterDO> selectMeterInOutPageDetailsMeterInfo(MeterInOutPageDetailsReqVO pageReqVO, List<Long> meterIds){
        if (meterIds == null || meterIds.isEmpty()) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }
        
        // 构建水表查询条件（使用LambdaQueryWrapperX）
        LambdaQueryWrapperX<MeterDO> meterQueryWrapper = new LambdaQueryWrapperX<>();

        // 通过水表ID列表查询
        meterQueryWrapper.in(MeterDO::getId, meterIds);

        // 添加水表相关的查询条件
        meterQueryWrapper
                .likeIfPresent(MeterDO::getSteelMark, pageReqVO.getSteelMark())
                .likeIfPresent(MeterDO::getCheckCode, pageReqVO.getCheckCode())
                .eqIfPresent(MeterDO::getMakerCode, pageReqVO.getMakerCode())
                .eqIfPresent(MeterDO::getModelCode, pageReqVO.getModelCode())
                .eqIfPresent(MeterDO::getCaliberCode, pageReqVO.getCaliberCode())
                .eqIfPresent(MeterDO::getRangeCode, pageReqVO.getRangeCode())
                .eqIfPresent(MeterDO::getType, pageReqVO.getType())
                .orderByDesc(MeterDO::getSteelMark)
                .orderByDesc(MeterDO::getMakeDate);

        return selectPage(pageReqVO, meterQueryWrapper);
    }

    default MeterDO selectOneBySteelMark(String steelMark){
        return selectOne(MeterDO::getSteelMark,steelMark);
    }

}