package cn.com.emsoft.sw.business.controller.admin.custnorule.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 户号规则新增/修改 Request VO")
@Data
public class CustNoRuleCreateReqVO {

    @Schema(description = "规则编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "规则编号不能为空")
    private String code;

    @Schema(description = "最大值", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "最大值不能为空")
    private Integer seqNo;

    @Schema(description = "规则表达式", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "规则表达式不能为空")
    private String seqRegex;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}