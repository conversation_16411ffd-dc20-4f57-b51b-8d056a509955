package cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 发票税率 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InvoiceTaxrateRespVO {

    @Schema(description = "主键Id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4553")
    @ExcelProperty("主键Id")
    private Long id;

    @Schema(description = "销售方/水司账户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "11378")
    @ExcelProperty("销售方/水司账户id")
    private Long accountId;

    @Schema(description = "销售方/水司账户名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "11378")
    @ExcelProperty("销售方/水司账户名称")
    private String accountName;

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("编码")
    private String code;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("名称")
    private String name;

    @Schema(description = "商品编码（唯一）")
    @ExcelProperty("商品编码（唯一）")
    private String productCode;

    @Schema(description = "零税率标识", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("零税率标识")
    private String taxRateMark;

    @Schema(description = "规格型号", example = "2")
    @ExcelProperty("规格型号")
    private String specificationType;

    @Schema(description = "税率百分比", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("税率百分比")
    private BigDecimal taxRate;

    @Schema(description = "项目单位")
    @ExcelProperty("项目单位")
    private String projectUnit;

    @Schema(description = "开票类型（多值，逗号分隔）", example = "2")
    @ExcelProperty("开票类型（多值，逗号分隔）")
    private String invoiceType;

    @Schema(description = "开票类型名称（多值，逗号分隔）", example = "2")
    @ExcelProperty("开票类型名称（多值，逗号分隔）")
    private String invoiceTypeName;

    @Schema(description = "状态：0-是，1-否", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态：0-是，1-否")
    private Short status;

}