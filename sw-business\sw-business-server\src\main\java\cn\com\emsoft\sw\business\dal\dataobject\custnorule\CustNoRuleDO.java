package cn.com.emsoft.sw.business.dal.dataobject.custnorule;

import cn.com.emsoft.sw.framework.common.validation.GetterRef;
import cn.com.emsoft.sw.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.com.emsoft.sw.framework.mybatis.core.dataobject.BaseDO;

/**
 * 户号规则 DO
 *
 * <AUTHOR>
 */
@TableName("biz_cust_no_rule")
@KeySequence("biz_cust_no_rule_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@GetterRef
public class CustNoRuleDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 规则编码
     */
    private String code;
    /**
     * 最大值
     */
    private Integer seqNo;
    /**
     * 规则正则表达式
     */
    private String seqRegex;
    /**
     * 是否绑定站点：0-否，1-是
     */
    private Short isBind;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态：0-是，1-否
     */
    private Short status;


}