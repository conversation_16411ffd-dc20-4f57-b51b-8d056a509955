package cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel.vo;

import cn.com.emsoft.sw.framework.common.pojo.SearchAndSortPageParam;
import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;

import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.com.emsoft.sw.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 户号规则和字典关系分页 Request VO")
@Data

public class DeptCustNoRuleRelPageReqVO extends SearchAndSortPageParam {

    @Schema(description = "部门代码，关联系统部门表")
    private String deptCode;

    @Schema(description = "户号规则id，关联户号规则表", example = "31376")
    private Long custRuleId;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}