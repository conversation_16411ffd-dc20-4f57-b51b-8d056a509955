package cn.com.emsoft.sw.business.dal.mysql.meterinout;

import java.util.*;

import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.MeterInOutPageDetailsReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterinout.vo.MeterInOutPageReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinout.MeterInOutDO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinout.MeterInOutDORef;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.com.emsoft.sw.framework.mybatis.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Mapper;


/**
 * 水表出/入库单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MeterInOutMapper extends BaseMapperX<MeterInOutDO> {

    default PageResult<MeterInOutDO> selectPage(MeterInOutPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<MeterInOutDO>()
                // 多字段模糊和排序
                .getSearchAndSort(MeterInOutDORef.class, reqVO.getSearch(), reqVO.getSearchContent(), reqVO.getSort())
                .eqIfPresent(MeterInOutDO::getDeptId, reqVO.getDeptId())
                .likeIfPresent(MeterInOutDO::getPayCode, reqVO.getPayCode())
                .likeIfPresent(MeterInOutDO::getStockCode, reqVO.getStockCode())
                .eqIfPresent(MeterInOutDO::getStockType, reqVO.getStockType())
                .inIfPresent(MeterInOutDO::getOperationUser, reqVO.getOperationUser())
                .betweenIfPresent(MeterInOutDO::getOperationTime,reqVO.getOperationTime())
                .orderByDesc(MeterInOutDO::getStockCode));
    }

    /**
     * 查询最新的一条入库单记录（按ID倒序）
     * 
     * @return 最新的入库单记录，如果不存在则返回null
     */
    default MeterInOutDO selectLatestStockIn() {
        return selectOne(new LambdaQueryWrapperX<MeterInOutDO>()
                .eq(MeterInOutDO::getStockType, 1) // 1=入库
                .orderByDesc(MeterInOutDO::getId)
                .last("LIMIT 1"));
    }

    /**
     * 查询最新的一条出库单记录（按ID倒序）
     * 
     * @return 最新的出库单记录，如果不存在则返回null
     */
    default MeterInOutDO selectLatestStockOut() {
        return selectOne(new LambdaQueryWrapperX<MeterInOutDO>()
                .eq(MeterInOutDO::getStockType, 2) // 2=出库
                .orderByDesc(MeterInOutDO::getId)
                .last("LIMIT 1"));
    }

    default List<MeterInOutDO> getMeterInOutDetailsPageList(MeterInOutPageDetailsReqVO pageReqVO){
        return selectList(new LambdaQueryWrapperX<MeterInOutDO>()
                .eqIfPresent(MeterInOutDO::getStockType, pageReqVO.getStockType())
                .eqIfPresent(MeterInOutDO::getDeptId, pageReqVO.getDeptId())
                .likeIfPresent(MeterInOutDO::getStockCode, pageReqVO.getStockCode())
                .inIfPresent(MeterInOutDO::getOperationUser, pageReqVO.getOperationUser())
                .likeIfPresent(MeterInOutDO::getPayCode, pageReqVO.getPayCode())
                .betweenIfPresent(MeterInOutDO::getOperationTime, pageReqVO.getOperationTime())
                .orderByDesc(MeterInOutDO::getStockCode));
    }

    //通过部门id获取到入库单记录列表
    default List<MeterInOutDO> getMeterInOutListByDeptId(Long deptId){
        return selectList(new LambdaQueryWrapperX<MeterInOutDO>()
                .eqIfPresent(MeterInOutDO::getDeptId,deptId)
                .orderByDesc(MeterInOutDO::getId));
    }

}