package cn.com.emsoft.sw.business.service.meterlog;

import cn.com.emsoft.sw.business.controller.admin.meterlog.vo.MeterLogPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.meterlog.vo.MeterLogPageRespVO;
import cn.com.emsoft.sw.business.controller.admin.meterlog.vo.MeterLogSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.meter.MeterDO;
import cn.com.emsoft.sw.business.dal.dataobject.meterinout.MeterInOutDO;
import cn.com.emsoft.sw.business.dal.dataobject.meterlog.MeterLogDO;
import cn.com.emsoft.sw.business.dal.mysql.meter.MeterMapper;
import cn.com.emsoft.sw.business.dal.mysql.meterinout.MeterInOutMapper;
import cn.com.emsoft.sw.business.dal.mysql.meterlog.MeterLogMapper;
import cn.com.emsoft.sw.business.enums.DictTypeConstants;
import cn.com.emsoft.sw.framework.common.enums.CommonStatusEnum;
import cn.com.emsoft.sw.framework.common.exception.ErrorCode;
import cn.com.emsoft.sw.framework.dict.core.DictFrameworkUtils;
import cn.com.emsoft.sw.module.system.api.dept.DeptApi;
import cn.com.emsoft.sw.module.system.api.dept.dto.DeptRespDTO;
import cn.com.emsoft.sw.module.system.api.user.AdminUserApi;
import cn.com.emsoft.sw.module.system.api.user.dto.AdminUserRespDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;

import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.*;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;


/**
 * 表务日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MeterLogServiceImpl implements MeterLogService {

    @Resource
    private MeterLogMapper meterLogMapper;

    @Resource
    private MeterMapper meterMapper;

    @Resource
    private MeterInOutMapper meterInOutMapper;

    @Resource
    private AdminUserApi adminUserApi;

    @Resource
    private DeptApi deptApi;

    @Override
    public Long createMeterLog(MeterLogSaveReqVO createReqVO) {
        // 插入
        MeterLogDO meterLog = BeanUtils.toBean(createReqVO, MeterLogDO.class);
        meterLogMapper.insert(meterLog);
        // 返回
        return meterLog.getId();
    }

    @Override
    public void updateMeterLog(MeterLogSaveReqVO updateReqVO) {
        // 校验存在
        validateMeterLogExists(updateReqVO.getId());
        // 更新
        MeterLogDO updateObj = BeanUtils.toBean(updateReqVO, MeterLogDO.class);
        meterLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteMeterLog(Long id) {
        // 校验存在
        validateMeterLogExists(id);
        // 删除
        meterLogMapper.deleteById(id);
    }

    @Override
        public void deleteMeterLogListByIds(List<Long> ids) {
        // 校验存在
        validateMeterLogExists(ids);
        // 删除
        meterLogMapper.deleteByIds(ids);
        }

    private void validateMeterLogExists(List<Long> ids) {
        List<MeterLogDO> list = meterLogMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(METER_LOG_NOT_EXISTS);
        }
    }

    private void validateMeterLogExists(Long id) {
        if (meterLogMapper.selectById(id) == null) {
            throw exception(METER_LOG_NOT_EXISTS);
        }
    }

    @Override
    public MeterLogDO getMeterLog(Long id) {
        return meterLogMapper.selectById(id);
    }

    @Override
    public PageResult<MeterLogDO> getMeterLogPage(MeterLogPageReqVO pageReqVO) {
        return meterLogMapper.selectPage(pageReqVO);
    }

    @Override
    public void batchCreateMeterInOutLogs(List<Long> meterIds, Long inOutId, Short logType, String result, String remark) {
        if (CollUtil.isEmpty(meterIds) || inOutId == null) {
            return;
        }

        // 构建批量日志记录
        List<MeterLogDO> logList = new ArrayList<>();
        for (Long meterId : meterIds) {
            if (meterId != null) {
                MeterLogDO log = MeterLogDO.builder()
                        .meterId(meterId)
                        .meterInOutId(inOutId)
                        .type(logType)
                        .result(result)
                        .remark(remark)
                        .status(CommonStatusEnum.ENABLE.getStatus().shortValue()) // 默认状态
                        .build();
                logList.add(log);
            }
        }

        // 批量插入日志记录
        if (CollUtil.isNotEmpty(logList)) {
            meterLogMapper.insertBatch(logList);
        }
    }

    @Override
    public void createMeterInOutLogs(Long meterId, Long inOutId, Short logType, String result, String remark) {
        if (meterId == null ){
            return;
        }
        MeterLogDO log = MeterLogDO.builder()
                .meterId(meterId)
                .meterInOutId(inOutId)
                .type(logType)
                .result(result)
                .remark(remark)
                .status(CommonStatusEnum.ENABLE.getStatus().shortValue()) // 默认状态
                .build();
        meterLogMapper.insert(log);
    }

    @Override
    public PageResult<MeterLogPageRespVO> getMeterLogPageWithDetails(MeterLogPageReqVO pageReqVO) {
        // 1. 验证输入参数
        validateMeterLogPageRequest(pageReqVO);

        // 2. 根据钢印号或条形码查询水表信息
        MeterDO meter = validateAndGetMeter(pageReqVO);

        // 3. 根据水表ID查询日志分页（避免SQL拼接，更安全）
        PageResult<MeterLogDO> logPageResult = meterLogMapper.selectPageByMeterId(meter.getId(), pageReqVO);
        
        if (CollUtil.isEmpty(logPageResult.getList())) {
            return new PageResult<>(Collections.emptyList(), logPageResult.getTotal());
        }

        // 4. 组装详细信息
        List<MeterLogPageRespVO> respVOList = buildMeterLogPageRespVOList(logPageResult.getList(), meter);

        return new PageResult<>(respVOList, logPageResult.getTotal());
    }

    /**
     * 验证请求参数
     */
    private void validateMeterLogPageRequest(MeterLogPageReqVO pageReqVO) {
        if (StrUtil.isBlank(pageReqVO.getSteelMark()) && StrUtil.isBlank(pageReqVO.getBarCode())) {
            throw exception(METER_LOG_PARAMS_MISSING);
        }
    }

    /**
     * 验证并获取水表信息
     * 优先级：钢印号 > 条形码
     * 如果两者都提供，优先使用钢印号查询
     */
    private MeterDO validateAndGetMeter(MeterLogPageReqVO pageReqVO) {
        MeterDO meter = null;
        String queryType = "";
        String queryValue = "";
        
        // 优先使用钢印号查询（钢印号是唯一的，更准确）
        if (StrUtil.isNotBlank(pageReqVO.getSteelMark())) {
            meter = meterMapper.selectOne(MeterDO::getSteelMark, pageReqVO.getSteelMark());
            queryType = "钢印号";
            queryValue = pageReqVO.getSteelMark();
        } 
        // 如果没有钢印号，使用条形码查询
        else if (StrUtil.isNotBlank(pageReqVO.getBarCode())) {
            meter = meterMapper.selectOne(MeterDO::getBarCode, pageReqVO.getBarCode());
            queryType = "条形码";
            queryValue = pageReqVO.getBarCode();
        }
        
        if (meter == null) {
            // 提供更详细的错误信息
            throw exception(new ErrorCode(1_025_020_001, 
                String.format("根据%s【%s】未找到对应的水表信息", queryType, queryValue)));
        }
        
        return meter;
    }

    /**
     * 组装水表日志响应VO列表
     */
    private List<MeterLogPageRespVO> buildMeterLogPageRespVOList(List<MeterLogDO> logList, MeterDO meter) {
        // 1. 收集所有需要查询的ID
        Set<Long> creatorIds = logList.stream()
                .map(MeterLogDO::getCreator)
                .filter(StrUtil::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toSet());

        Set<Long> inOutIds = logList.stream()
                .map(MeterLogDO::getMeterInOutId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 2. 批量查询关联数据
        Map<Long, AdminUserRespDTO> userMap = buildUserMap(creatorIds);
        Map<Long, MeterInOutDO> inOutMap = buildInOutMap(inOutIds);
        Map<Long, DeptRespDTO> deptMap = buildDeptMap(meter, inOutMap);

        // 3. 组装结果
        return logList.stream().map(log -> buildMeterLogPageRespVO(log, meter, userMap, inOutMap, deptMap))
                .collect(Collectors.toList());
    }

    /**
     * 构建用户信息Map
     */
    private Map<Long, AdminUserRespDTO> buildUserMap(Set<Long> creatorIds) {
        if (CollUtil.isEmpty(creatorIds)) {
            return Collections.emptyMap();
        }
        
        try {
            List<AdminUserRespDTO> users = adminUserApi.getUserList(creatorIds).getCheckedData();
            return users.stream().collect(Collectors.toMap(AdminUserRespDTO::getId, user -> user));
        } catch (Exception e) {
            // 如果获取用户信息失败，记录日志但不影响主流程
            return Collections.emptyMap();
        }
    }

    /**
     * 构建出入库信息Map
     */
    private Map<Long, MeterInOutDO> buildInOutMap(Set<Long> inOutIds) {
        if (CollUtil.isEmpty(inOutIds)) {
            return Collections.emptyMap();
        }
        
        List<MeterInOutDO> inOutList = meterInOutMapper.selectByIds(inOutIds);
        return inOutList.stream().collect(Collectors.toMap(MeterInOutDO::getId, inOut -> inOut));
    }

    /**
     * 构建部门信息Map
     */
    private Map<Long, DeptRespDTO> buildDeptMap(MeterDO meter, Map<Long, MeterInOutDO> inOutMap) {
        Set<Long> deptIds = new HashSet<>();
        
        // 添加水表的部门ID
        if (meter.getDeptId() != null) {
            deptIds.add(meter.getDeptId());
        }
        
        // 添加出入库单的部门ID
        inOutMap.values().stream()
                .map(MeterInOutDO::getDeptId)
                .filter(Objects::nonNull)
                .forEach(deptIds::add);
        
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyMap();
        }
        
        try {
            List<DeptRespDTO> depts = deptApi.getDeptList(deptIds).getCheckedData();
            return depts.stream().collect(Collectors.toMap(DeptRespDTO::getId, dept -> dept));
        } catch (Exception e) {
            // 如果获取部门信息失败，记录日志但不影响主流程
            return Collections.emptyMap();
        }
    }

    /**
     * 构建单个水表日志响应VO
     */
    private MeterLogPageRespVO buildMeterLogPageRespVO(MeterLogDO log, MeterDO meter, 
            Map<Long, AdminUserRespDTO> userMap, Map<Long, MeterInOutDO> inOutMap, 
            Map<Long, DeptRespDTO> deptMap) {
        
        MeterLogPageRespVO respVO = BeanUtils.toBean(log, MeterLogPageRespVO.class);
        
        // 设置创建人姓名
        if (StrUtil.isNotBlank(log.getCreator())) {
            AdminUserRespDTO user = userMap.get(Long.valueOf(log.getCreator()));
            if (user != null) {
                respVO.setCreatorName(user.getNickname());
            }
        }
        
        // 设置日志类型名称
        respVO.setTypeName(DictFrameworkUtils.parseDictDataLabel(
                DictTypeConstants.METER_LOG_TYPE,
                String.valueOf(log.getType())
        ));


        // 设置水表信息
        respVO.setMeterInfo(buildMeterInfo(meter, deptMap));
        
        // 设置出入库信息（如果有）
        if (log.getMeterInOutId() != null) {
            MeterInOutDO inOut = inOutMap.get(log.getMeterInOutId());
            if (inOut != null) {
                respVO.setMeterInOutInfo(buildMeterInOutInfo(inOut, deptMap));
            }
        }
        
        return respVO;
    }

    /**
     * 构建水表信息
     */
    private MeterLogPageRespVO.meterInfo buildMeterInfo(MeterDO meter, Map<Long, DeptRespDTO> deptMap) {
        MeterLogPageRespVO.meterInfo meterInfo = new MeterLogPageRespVO.meterInfo();
        meterInfo.setId(meter.getId());
        meterInfo.setDeptId(meter.getDeptId());
        meterInfo.setSteelMark(meter.getSteelMark());
        meterInfo.setSealNumber(meter.getSealNumber());
        
        // 设置部门名称
        if (meter.getDeptId() != null) {
            DeptRespDTO dept = deptMap.get(meter.getDeptId());
            if (dept != null) {
                meterInfo.setDeptName(dept.getName());
            }
        }
        
        return meterInfo;
    }

    /**
     * 构建出入库信息
     */
    private MeterLogPageRespVO.meterInOutInfo buildMeterInOutInfo(MeterInOutDO inOut, Map<Long, DeptRespDTO> deptMap) {
        MeterLogPageRespVO.meterInOutInfo inOutInfo = new MeterLogPageRespVO.meterInOutInfo();
        inOutInfo.setId(inOut.getId());
        inOutInfo.setDeptId(inOut.getDeptId());
        inOutInfo.setPayCode(inOut.getPayCode());
        inOutInfo.setStockCode(inOut.getStockCode());
        inOutInfo.setStockType(inOut.getStockType());
        inOutInfo.setOperationUser(inOut.getOperationUser());
        inOutInfo.setOperationTime(inOut.getOperationTime());
        inOutInfo.setRemark(inOut.getRemark());
        
        // 设置部门名称
        if (inOut.getDeptId() != null) {
            DeptRespDTO dept = deptMap.get(inOut.getDeptId());
            if (dept != null) {
                inOutInfo.setDeptName(dept.getName());
            }
        }
        
        return inOutInfo;
    }

}