package cn.com.emsoft.sw.business.controller.admin.invoicetaxrate;

import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxrateCreateReqVO;
import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxratePageReqVO;
import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxrateRespVO;
import cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo.InvoiceTaxrateUpdateReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.invoicetaxrate.InvoiceTaxrateDO;
import cn.com.emsoft.sw.business.service.invoicetaxrate.InvoiceTaxrateService;
import cn.com.emsoft.sw.framework.common.pojo.PageParam;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.pojo.CommonResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;
import static cn.com.emsoft.sw.framework.common.pojo.CommonResult.success;

import cn.com.emsoft.sw.framework.excel.core.util.ExcelUtils;

import cn.com.emsoft.sw.framework.apilog.core.annotation.ApiAccessLog;
import static cn.com.emsoft.sw.framework.apilog.core.enums.OperateTypeEnum.*;



@Tag(name = "管理后台 - 发票税率")
@RestController
@RequestMapping("/business/invoice-taxrate")
@Validated
public class InvoiceTaxrateController {

    @Resource
    private InvoiceTaxrateService invoiceTaxrateService;

    @PostMapping("/create")
    @Operation(summary = "创建发票税率")
    @PreAuthorize("@ss.hasPermission('business:invoice-taxrate:create')")
    public CommonResult<Long> createInvoiceTaxrate(@Valid @RequestBody InvoiceTaxrateCreateReqVO createReqVO) {
        return success(invoiceTaxrateService.createInvoiceTaxrate(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新发票税率")
    @PreAuthorize("@ss.hasPermission('business:invoice-taxrate:update')")
    public CommonResult<Boolean> updateInvoiceTaxrate(@Valid @RequestBody InvoiceTaxrateUpdateReqVO updateReqVO) {
        invoiceTaxrateService.updateInvoiceTaxrate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除发票税率")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('business:invoice-taxrate:delete')")
    public CommonResult<Boolean> deleteInvoiceTaxrate(@RequestParam("id") Long id) {
        invoiceTaxrateService.deleteInvoiceTaxrate(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除发票税率")
                @PreAuthorize("@ss.hasPermission('business:invoice-taxrate:delete')")
    public CommonResult<Boolean> deleteInvoiceTaxrateList(@RequestParam("ids") List<Long> ids) {
        invoiceTaxrateService.deleteInvoiceTaxrateListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得发票税率")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:invoice-taxrate:query')")
    public CommonResult<InvoiceTaxrateRespVO> getInvoiceTaxrate(@RequestParam("id") Long id) {
        InvoiceTaxrateDO invoiceTaxrate = invoiceTaxrateService.getInvoiceTaxrate(id);
        return success(BeanUtils.toBean(invoiceTaxrate, InvoiceTaxrateRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得发票税率分页")
    @PreAuthorize("@ss.hasPermission('business:invoice-taxrate:query')")
    public CommonResult<PageResult<InvoiceTaxrateRespVO>> getInvoiceTaxratePage(@Valid InvoiceTaxratePageReqVO pageReqVO) {
        return success(invoiceTaxrateService.getInvoiceTaxratePage(pageReqVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出发票税率 Excel")
    @PreAuthorize("@ss.hasPermission('business:invoice-taxrate:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInvoiceTaxrateExcel(@Valid InvoiceTaxratePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InvoiceTaxrateRespVO> list = invoiceTaxrateService.getInvoiceTaxratePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "发票税率.xls", "数据", InvoiceTaxrateRespVO.class,
                        BeanUtils.toBean(list, InvoiceTaxrateRespVO.class));
    }

}