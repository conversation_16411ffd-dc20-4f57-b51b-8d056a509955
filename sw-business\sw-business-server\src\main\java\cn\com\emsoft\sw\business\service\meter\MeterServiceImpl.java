package cn.com.emsoft.sw.business.service.meter;

import cn.com.emsoft.sw.business.controller.admin.meter.vo.*;
import cn.com.emsoft.sw.business.dal.dataobject.meter.MeterDO;
import cn.com.emsoft.sw.business.dal.dataobject.metercaliber.MeterCaliberDO;
import cn.com.emsoft.sw.business.dal.dataobject.metermaker.MeterMakerDO;
import cn.com.emsoft.sw.business.dal.dataobject.metermodel.MeterModelDO;
import cn.com.emsoft.sw.business.dal.dataobject.meterrange.MeterRangeDO;
import cn.com.emsoft.sw.business.dal.mysql.meter.MeterMapper;
import cn.com.emsoft.sw.business.enums.DictTypeConstants;
import cn.com.emsoft.sw.business.enums.meterLog.MeterLogEnum;
import cn.com.emsoft.sw.business.service.metercaliber.MeterCaliberService;
import cn.com.emsoft.sw.business.service.meterlog.MeterLogService;
import cn.com.emsoft.sw.business.service.metermaker.MeterMakerService;
import cn.com.emsoft.sw.business.service.metermodel.MeterModelService;
import cn.com.emsoft.sw.business.service.meterrange.MeterRangeService;
import cn.com.emsoft.sw.framework.dict.core.DictFrameworkUtils;
import cn.com.emsoft.sw.module.system.api.dept.DeptApi;
import cn.com.emsoft.sw.module.system.api.dept.dto.DeptRespDTO;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;

import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;

import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.*;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 水表信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MeterServiceImpl implements MeterService {

    @Resource
    private MeterMapper meterMapper;

    @Resource
    private MeterMakerService meterMakerService;

    @Resource
    private MeterModelService meterModelService;

    @Resource
    private MeterCaliberService meterCaliberService;

    @Resource
    private MeterRangeService meterRangeService;

    @Resource
    private MeterLogService meterLogService;

    @Resource
    private DeptApi deptApi;

    @Override
    public Long createMeter(MeterSaveReqVO createReqVO) {
        // 插入
        MeterDO meter = BeanUtils.toBean(createReqVO, MeterDO.class);
        meterMapper.insert(meter);
        // 返回
        return meter.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMeter(MeterUpdateReqVO updateReqVO) {
        // 校验存在
        validateMeterExists(updateReqVO.getId());
        //校验水表厂家、水表型号、水表口径、水表量程、水表类型存在性以及状态
        validateMeterUpdateInfosByCode(updateReqVO.getMakerCode(),updateReqVO.getModelCode(),updateReqVO.getCaliberCode(),updateReqVO.getRangeCode(),updateReqVO.getType());
        // 更新
        MeterDO updateObj = BeanUtils.toBean(updateReqVO, MeterDO.class);
        meterMapper.updateById(updateObj);

        //异步记录日志
        asyncCreateMeterInOutLog(updateObj.getId(),null, MeterLogEnum.METER_INFO_UPDATE.getType(), "成功","水表基础信息修改");
    }

    @Override
    public void updateMeterSteelMarkAndSealNumber(MeterUpdateSpecialInfoReqVO updateReqVO) {
        //校验存在
        validateMeterExists(updateReqVO.getId());
        //校验钢印号是否重复
        validateMeterSteelMarkExists(updateReqVO.getId(),updateReqVO.getSteelMark());
        //更新水表信息
        MeterDO updateObj = BeanUtils.toBean(updateReqVO, MeterDO.class);
        meterMapper.updateById(updateObj);
        //异步记录日志
        asyncCreateMeterInOutLog(updateObj.getId(),null, MeterLogEnum.METER_INFO_UPDATE.getType(), "成功","水表基础信息修改");
    }

    @Override
    public void deleteMeter(Long id) {
        // 校验存在
        validateMeterExists(id);
        // 删除
        meterMapper.deleteById(id);
    }

    @Override
    public void deleteMeterListByIds(List<Long> ids) {
        // 校验存在
        validateMeterExists(ids);
        // 删除
        meterMapper.deleteByIds(ids);
    }

    private void validateMeterExists(List<Long> ids) {
        List<MeterDO> list = meterMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(METER_NOT_EXISTS);
        }
    }

    private void validateMeterExists(Long id) {
        if (meterMapper.selectById(id) == null) {
            throw exception(METER_NOT_EXISTS);
        }
    }

    private void validateMeterSteelMarkExists(Long id,String steelMark){
        MeterDO meterDO = meterMapper.selectOneBySteelMark(steelMark);
        if (meterDO == null){
            return;
        }
        if (id == null|| ObjUtil.notEqual(id,meterDO.getId())){
            throw exception(METER_IN_STEEL_MARK_EXISTS);
        }
    }

    private void validateMeterUpdateInfosByCode(String makerCode,String modelCode,String caliberCode,String rangeCode,Short type){
        //校验水表厂家
        meterMakerService.validateMeterMakerExistsByCode(makerCode);
        //校验水表型号
        meterModelService.validateMeterModelExistsAndEnabledByCode(modelCode);
        //校验水表口径
        meterCaliberService.validateMeterCaliberExistsAndEnabledByCode(caliberCode);
        //校验水表量程
        meterRangeService.validateMeterRangeExistsAndEnabledByCode(rangeCode);
        //校验水表类型
        if (type != null){
            String typeName = DictFrameworkUtils.parseDictDataLabel(
                    DictTypeConstants.METER_OTH_TYPE,
                    String.valueOf(type)
            );
            if (StrUtil.isEmpty(typeName)){
                throw exception(METER_IN_TYPE_NOT_EXISTS);
            }
        }
    }

    /**
     * 异步创建水表入库日志
     *
     * @param meterId 水表ID列表
     * @param inOutId 出入库单ID
     */
    @Async
    protected void asyncCreateMeterInOutLog(Long meterId, Long inOutId,Short logType ,String result,String remark) {
        try {
            meterLogService.createMeterInOutLogs(meterId, inOutId, logType, result, remark);
        } catch (Exception e) {
            // 日志记录失败不应该影响主业务流程，仅记录错误
            // 这里可以使用日志框架记录异常信息
            // 后续使用mq重试处理
        }
    }

    @Override
    public MeterDO getMeter(Long id) {
        return meterMapper.selectById(id);
    }

    @Override
    public MeterDO getMeterDetails(MeterDetailsReqVO reqVO) {
        return meterMapper.getMeterDetails(reqVO);
    }


    @Override
    public PageResult<MeterPageRespVO> getMeterPageWithDetails(MeterPageReqVO pageReqVO) {
        // 1. 先查询基础的水表分页数据
        PageResult<MeterDO> meterPageResult = meterMapper.selectPage(pageReqVO);
        
        if (CollUtil.isEmpty(meterPageResult.getList())) {
            return new PageResult<>(Collections.emptyList(), meterPageResult.getTotal());
        }

        List<MeterDO> meterList = meterPageResult.getList();

        // 2. 收集所有需要查询的代码
        Set<String> makerCodes = meterList.stream()
                .map(MeterDO::getMakerCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        Set<String> modelCodes = meterList.stream()
                .map(MeterDO::getModelCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        Set<String> caliberCodes = meterList.stream()
                .map(MeterDO::getCaliberCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        Set<String> rangeCodes = meterList.stream()
                .map(MeterDO::getRangeCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        
        // 收集部门ID（直接从水表对象获取，简化逻辑）
        Set<Long> deptIds = meterList.stream()
                .map(MeterDO::getDeptId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 3. 批量查询相关信息
        Map<String, MeterMakerDO> makerMap = buildMakerCodeMap(makerCodes);
        Map<String, MeterModelDO> modelMap = buildModelCodeMap(modelCodes);
        Map<String, MeterCaliberDO> caliberMap = buildCaliberCodeMap(caliberCodes);
        Map<String, MeterRangeDO> rangeMap = buildRangeCodeMap(rangeCodes);
        Map<Long, DeptRespDTO> deptMap = Collections.emptyMap();
        if (!deptIds.isEmpty()) {
            deptMap = deptApi.getDeptMap(deptIds);
        }

        // 4. 组装返回数据
        List<MeterPageRespVO> resultList = new ArrayList<>();
        for (MeterDO meter : meterList) {
            MeterPageRespVO respVO = BeanUtils.toBean(meter, MeterPageRespVO.class);
            
            // 设置厂家信息
            MeterMakerDO maker = makerMap.get(meter.getMakerCode());
            if (maker != null) {
                MeterPageRespVO.MakerInfo makerInfo = new MeterPageRespVO.MakerInfo();
                makerInfo.setId(maker.getId());
                makerInfo.setCode(maker.getCode());
                makerInfo.setName(maker.getName());
                respVO.setMakerInfo(makerInfo);
                //为了解决excel导出的问题，增加名称字段
                respVO.setMakerName(makerInfo.getName());
            }
            
            // 设置型号信息
            MeterModelDO model = modelMap.get(meter.getModelCode());
            if (model != null) {
                MeterPageRespVO.ModelInfo modelInfo = new MeterPageRespVO.ModelInfo();
                modelInfo.setId(model.getId());
                modelInfo.setCode(model.getCode());
                modelInfo.setName(model.getName());
                respVO.setModelInfo(modelInfo);
                //为了解决excel导出的问题，增加名称字段
                respVO.setModelName(modelInfo.getName());
            }
            
            // 设置口径信息
            MeterCaliberDO caliber = caliberMap.get(meter.getCaliberCode());
            if (caliber != null) {
                MeterPageRespVO.CaliberInfo caliberInfo = new MeterPageRespVO.CaliberInfo();
                caliberInfo.setId(caliber.getId());
                caliberInfo.setCode(caliber.getCode());
                caliberInfo.setName(caliber.getName());
                respVO.setCaliberInfo(caliberInfo);
                //为了解决excel导出的问题，增加名称字段
                respVO.setCaliberName(caliberInfo.getName());
            }
            
            // 设置量程信息
            MeterRangeDO range = rangeMap.get(meter.getRangeCode());
            if (range != null) {
                MeterPageRespVO.RangeInfo rangeInfo = new MeterPageRespVO.RangeInfo();
                rangeInfo.setId(range.getId());
                rangeInfo.setCode(range.getCode());
                rangeInfo.setName(range.getName());
                respVO.setRangeInfo(rangeInfo);
                //为了解决excel导出的问题，增加名称字段
                respVO.setRangeName(rangeInfo.getName());
            }
            
            // 设置部门信息（直接使用水表中的部门信息，简化逻辑）
            Long deptId = meter.getDeptId();
            if (deptId != null) {
                respVO.setDeptId(deptId);
                DeptRespDTO dept = deptMap.get(deptId);
                if (dept != null) {
                    respVO.setDeptName(dept.getName());
                }
            }

            // 设置水表类型名称（使用字典转换）
            if (meter.getType() != null) {
                String typeName = DictFrameworkUtils.parseDictDataLabel(
                        DictTypeConstants.METER_OTH_TYPE,
                        String.valueOf(respVO.getType())
                );
                respVO.setTypeName(typeName);
            }
            // 设置表务状态名称（使用字典转换）
            if (meter.getMeterStatus() != null) {
                String typeName = DictFrameworkUtils.parseDictDataLabel(
                        DictTypeConstants.MW_STATE,
                        String.valueOf(respVO.getMeterStatus())
                );
                respVO.setMeterStatusName(typeName);
            }

            
            resultList.add(respVO);
        }

        return new PageResult<>(resultList, meterPageResult.getTotal());
    }

    /**
     * 构建厂家代码Map缓存（按需查询）
     */
    private Map<String, MeterMakerDO> buildMakerCodeMap(Set<String> makerCodes) {
        if (CollUtil.isEmpty(makerCodes)) {
            return new HashMap<>();
        }
        List<MeterMakerDO> makers = meterMakerService.getEnabledMeterMakersByCodes(makerCodes);
        return makers.stream().collect(Collectors.toMap(
                MeterMakerDO::getCode, 
                maker -> maker, 
                (existing, replacement) -> existing));
    }

    /**
     * 构建型号代码Map缓存（按需查询）
     */
    private Map<String, MeterModelDO> buildModelCodeMap(Set<String> modelCodes) {
        if (CollUtil.isEmpty(modelCodes)) {
            return new HashMap<>();
        }
        List<MeterModelDO> models = meterModelService.getEnabledMeterModelsByCodes(modelCodes);
        return models.stream().collect(Collectors.toMap(
                MeterModelDO::getCode, 
                model -> model, 
                (existing, replacement) -> existing));
    }

    /**
     * 构建口径代码Map缓存（按需查询）
     */
    private Map<String, MeterCaliberDO> buildCaliberCodeMap(Set<String> caliberCodes) {
        if (CollUtil.isEmpty(caliberCodes)) {
            return new HashMap<>();
        }
        List<MeterCaliberDO> calibers = meterCaliberService.getEnabledMeterCalibersByCodes(caliberCodes);
        return calibers.stream().collect(Collectors.toMap(
                MeterCaliberDO::getCode, 
                caliber -> caliber, 
                (existing, replacement) -> existing));
    }

    /**
     * 构建量程代码Map缓存（按需查询）
     */
    private Map<String, MeterRangeDO> buildRangeCodeMap(Set<String> rangeCodes) {
        if (CollUtil.isEmpty(rangeCodes)) {
            return new HashMap<>();
        }
        List<MeterRangeDO> ranges = meterRangeService.getEnabledMeterRangesByCodes(rangeCodes);
        return ranges.stream().collect(Collectors.toMap(
                MeterRangeDO::getCode, 
                range -> range, 
                (existing, replacement) -> existing));
    }

}