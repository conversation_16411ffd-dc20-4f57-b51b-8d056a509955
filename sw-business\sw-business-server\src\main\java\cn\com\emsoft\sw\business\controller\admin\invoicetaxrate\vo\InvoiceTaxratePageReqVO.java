package cn.com.emsoft.sw.business.controller.admin.invoicetaxrate.vo;

import cn.com.emsoft.sw.framework.common.pojo.SearchAndSortPageParam;
import jakarta.validation.constraints.NotNull;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;

@Schema(description = "管理后台 - 发票税率分页 Request VO")
@Data
public class InvoiceTaxratePageReqVO extends SearchAndSortPageParam {

    @Schema(description = "销售方/水司账户id", example = "11378")
    @NotNull(message = "水司账户id不能为空，请联系管理员处理")
    private Long accountId;

}