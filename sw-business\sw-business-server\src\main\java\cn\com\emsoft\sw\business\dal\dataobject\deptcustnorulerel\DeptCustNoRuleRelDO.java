package cn.com.emsoft.sw.business.dal.dataobject.deptcustnorulerel;

import cn.com.emsoft.sw.framework.common.validation.GetterRef;
import cn.com.emsoft.sw.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.com.emsoft.sw.framework.mybatis.core.dataobject.BaseDO;

/**
 * 户号规则和字典关系 DO
 *
 * <AUTHOR>
 */
@TableName("biz_dept_cust_no_rule_rel")
@KeySequence("biz_dept_cust_no_rule_rel_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@GetterRef
public class DeptCustNoRuleRelDO extends TenantBaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 部门代码，关联系统部门表
     */
    private String deptCode;
    /**
     * 户号规则id，关联户号规则表
     */
    private Long custRuleId;
    /**
     * 备注
     */
    private String remark;
    /**
     * 状态：0-否，1-是
     */
    private Short status;


}