package cn.com.emsoft.sw.business.service.deptcustnorulerel;

import cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel.vo.DeptCustNoRuleRelPageReqVO;
import cn.com.emsoft.sw.business.controller.admin.deptcustnorulerel.vo.DeptCustNoRuleRelSaveReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.deptcustnorulerel.DeptCustNoRuleRelDO;
import cn.com.emsoft.sw.business.dal.mysql.deptcustnorulerel.DeptCustNoRuleRelMapper;
import cn.hutool.core.collection.CollUtil;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;


import static cn.com.emsoft.sw.business.enums.ErrorCodeConstants.DEPT_CUST_NO_RULE_REL_NOT_EXISTS;
import static cn.com.emsoft.sw.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.com.emsoft.sw.framework.common.util.collection.CollectionUtils.convertList;
import static cn.com.emsoft.sw.framework.common.util.collection.CollectionUtils.diffList;

/**
 * 户号规则和字典关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DeptCustNoRuleRelServiceImpl implements DeptCustNoRuleRelService {

    @Resource
    private DeptCustNoRuleRelMapper deptCustNoRuleRelMapper;

    @Override
    public Long createDeptCustNoRuleRel(DeptCustNoRuleRelSaveReqVO createReqVO) {
        // 插入
        DeptCustNoRuleRelDO deptCustNoRuleRel = BeanUtils.toBean(createReqVO, DeptCustNoRuleRelDO.class);
        deptCustNoRuleRelMapper.insert(deptCustNoRuleRel);
        // 返回
        return deptCustNoRuleRel.getId();
    }

    @Override
    public void updateDeptCustNoRuleRel(DeptCustNoRuleRelSaveReqVO updateReqVO) {
        // 校验存在
        validateDeptCustNoRuleRelExists(updateReqVO.getId());
        // 更新
        DeptCustNoRuleRelDO updateObj = BeanUtils.toBean(updateReqVO, DeptCustNoRuleRelDO.class);
        deptCustNoRuleRelMapper.updateById(updateObj);
    }

    @Override
    public void deleteDeptCustNoRuleRel(Long id) {
        // 校验存在
        validateDeptCustNoRuleRelExists(id);
        // 删除
        deptCustNoRuleRelMapper.deleteById(id);
    }

    @Override
        public void deleteDeptCustNoRuleRelListByIds(List<Long> ids) {
        // 校验存在
        validateDeptCustNoRuleRelExists(ids);
        // 删除
        deptCustNoRuleRelMapper.deleteByIds(ids);
        }

    private void validateDeptCustNoRuleRelExists(List<Long> ids) {
        List<DeptCustNoRuleRelDO> list = deptCustNoRuleRelMapper.selectByIds(ids);
        if (CollUtil.isEmpty(list) || list.size() != ids.size()) {
            throw exception(DEPT_CUST_NO_RULE_REL_NOT_EXISTS);
        }
    }

    private void validateDeptCustNoRuleRelExists(Long id) {
        if (deptCustNoRuleRelMapper.selectById(id) == null) {
            throw exception(DEPT_CUST_NO_RULE_REL_NOT_EXISTS);
        }
    }

    @Override
    public DeptCustNoRuleRelDO getDeptCustNoRuleRel(Long id) {
        return deptCustNoRuleRelMapper.selectById(id);
    }

    @Override
    public PageResult<DeptCustNoRuleRelDO> getDeptCustNoRuleRelPage(DeptCustNoRuleRelPageReqVO pageReqVO) {
        return deptCustNoRuleRelMapper.selectPage(pageReqVO);
    }

}