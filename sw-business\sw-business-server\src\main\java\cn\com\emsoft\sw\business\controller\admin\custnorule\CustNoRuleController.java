package cn.com.emsoft.sw.business.controller.admin.custnorule;

import cn.com.emsoft.sw.business.controller.admin.custnorule.vo.CustNoRuleCreateReqVO;
import cn.com.emsoft.sw.business.controller.admin.custnorule.vo.CustNoRulePageReqVO;
import cn.com.emsoft.sw.business.controller.admin.custnorule.vo.CustNoRuleRespVO;
import cn.com.emsoft.sw.business.controller.admin.custnorule.vo.CustNoRuleUpdateReqVO;
import cn.com.emsoft.sw.business.dal.dataobject.custnorule.CustNoRuleDO;
import cn.com.emsoft.sw.business.service.custnorule.CustNoRuleService;
import cn.com.emsoft.sw.framework.common.pojo.PageParam;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.com.emsoft.sw.framework.common.pojo.PageResult;
import cn.com.emsoft.sw.framework.common.pojo.CommonResult;
import cn.com.emsoft.sw.framework.common.util.object.BeanUtils;
import static cn.com.emsoft.sw.framework.common.pojo.CommonResult.success;

import cn.com.emsoft.sw.framework.excel.core.util.ExcelUtils;

import cn.com.emsoft.sw.framework.apilog.core.annotation.ApiAccessLog;
import static cn.com.emsoft.sw.framework.apilog.core.enums.OperateTypeEnum.*;


@Tag(name = "管理后台 - 户号规则")
@RestController
@RequestMapping("/business/cust-no-rule")
@Validated
public class CustNoRuleController {

    @Resource
    private CustNoRuleService custNoRuleService;

    @PostMapping("/create")
    @Operation(summary = "创建户号规则")
    @PreAuthorize("@ss.hasPermission('business:cust-no-rule:create')")
    public CommonResult<Long> createCustNoRule(@Valid @RequestBody CustNoRuleCreateReqVO createReqVO) {
        return success(custNoRuleService.createCustNoRule(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新户号规则")
    @PreAuthorize("@ss.hasPermission('business:cust-no-rule:update')")
    public CommonResult<Boolean> updateCustNoRule(@Valid @RequestBody CustNoRuleUpdateReqVO updateReqVO) {
        custNoRuleService.updateCustNoRule(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除户号规则")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('business:cust-no-rule:delete')")
    public CommonResult<Boolean> deleteCustNoRule(@RequestParam("id") Long id) {
        custNoRuleService.deleteCustNoRule(id);
        return success(true);
    }

    @DeleteMapping("/delete-list")
    @Parameter(name = "ids", description = "编号", required = true)
    @Operation(summary = "批量删除户号规则")
                @PreAuthorize("@ss.hasPermission('business:cust-no-rule:delete')")
    public CommonResult<Boolean> deleteCustNoRuleList(@RequestParam("ids") List<Long> ids) {
        custNoRuleService.deleteCustNoRuleListByIds(ids);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得户号规则")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('business:cust-no-rule:query')")
    public CommonResult<CustNoRuleRespVO> getCustNoRule(@RequestParam("id") Long id) {
        CustNoRuleDO custNoRule = custNoRuleService.getCustNoRule(id);
        return success(BeanUtils.toBean(custNoRule, CustNoRuleRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得户号规则分页")
    @PreAuthorize("@ss.hasPermission('business:cust-no-rule:query')")
    public CommonResult<PageResult<CustNoRuleRespVO>> getCustNoRulePage(@Valid CustNoRulePageReqVO pageReqVO) {
        PageResult<CustNoRuleDO> pageResult = custNoRuleService.getCustNoRulePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, CustNoRuleRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出户号规则 Excel")
    @PreAuthorize("@ss.hasPermission('business:cust-no-rule:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCustNoRuleExcel(@Valid CustNoRulePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<CustNoRuleDO> list = custNoRuleService.getCustNoRulePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "户号规则.xls", "数据", CustNoRuleRespVO.class,
                        BeanUtils.toBean(list, CustNoRuleRespVO.class));
    }

}